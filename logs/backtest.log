2025-07-10 21:09:20 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:09:20 - forex_backtest.data.data_loader - INFO - Downloading AUDUSD data from 2023-01-01 to 2024-01-01 (5m)
2025-07-10 21:09:21 - yfinance - ERROR - $AUDUSD=X: possibly delisted; no price data found  (5m 2023-01-01 -> 2024-01-01) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1704067200. The requested range must be within the last 60 days.")
2025-07-10 21:09:21 - root - ERROR - Backtest execution failed: Failed to load data from yfinance: No data returned for AUDUSD
2025-07-10 21:09:21 - __main__ - ERROR - Backtest failed: Failed to load data from yfinance: No data returned for AUDUSD
2025-07-10 21:10:17 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:10:17 - forex_backtest.data.data_loader - INFO - Downloading EURUSD data from 2023-01-01 to 2023-12-31 (5m)
2025-07-10 21:10:18 - yfinance - ERROR - $EURUSD=X: possibly delisted; no price data found  (5m 2023-01-01 -> 2023-12-31) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1703980800. The requested range must be within the last 60 days.")
2025-07-10 21:10:18 - root - ERROR - Backtest execution failed: Failed to load data from yfinance: No data returned for EURUSD
2025-07-10 21:10:18 - __main__ - ERROR - Backtest failed: Failed to load data from yfinance: No data returned for EURUSD
2025-07-10 21:33:29 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:33:48 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:34:04 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:34:04 - forex_backtest.data.local_loader - INFO - Loading local data from examples/local_data/AUDUSD_5m.csv
2025-07-10 21:34:04 - forex_backtest.data.local_loader - INFO - Processed data: 37 bars from 2023-01-01 00:00:00 to 2023-01-01 03:00:00
2025-07-10 21:34:04 - forex_backtest.data.local_loader - INFO - Filtered data from 2023-01-01 to 2023-01-02: 37 bars
2025-07-10 21:34:04 - forex_backtest.data.market_data - INFO - Market data validated: 37 bars from 2023-01-01 00:00:00 to 2023-01-01 03:00:00
2025-07-10 21:34:04 - forex_backtest.data.local_loader - INFO - Loaded 37 bars from local file
2025-07-10 21:34:12 - forex_backtest.config.settings - INFO - Configuration loaded successfully from examples/local_data_config.yaml
2025-07-10 21:43:32 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:44:52 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:45:45 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:47:51 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:58:13 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:58:31 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:58:31 - forex_backtest.data.data_loader - INFO - Downloading AUDNZD data from 2023-01-01 to 2023-02-15 (5m)
2025-07-10 21:58:32 - yfinance - ERROR - $AUDNZD=X: possibly delisted; no price data found  (5m 2023-01-01 -> 2023-02-15) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1676419200. The requested range must be within the last 60 days.")
2025-07-10 21:58:32 - root - ERROR - Backtest execution failed: Failed to load data from yfinance: No data returned for AUDNZD
2025-07-10 21:58:32 - __main__ - ERROR - Backtest failed: Failed to load data from yfinance: No data returned for AUDNZD
2025-07-10 21:59:34 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-10 21:59:34 - forex_backtest.data.local_loader - INFO - Loading local data from examples/local_data/AUDNZD_5m.csv
2025-07-10 21:59:34 - forex_backtest.data.local_loader - INFO - Processed data: 37 bars from 2023-01-01 00:00:00 to 2023-01-01 03:00:00
2025-07-10 21:59:34 - forex_backtest.data.local_loader - INFO - Filtered data from 2023-01-01 to 2023-01-02: 37 bars
2025-07-10 21:59:34 - forex_backtest.data.market_data - INFO - Market data validated: 37 bars from 2023-01-01 00:00:00 to 2023-01-01 03:00:00
2025-07-10 21:59:34 - forex_backtest.data.local_loader - INFO - Loaded 37 bars from local file
2025-07-11 05:23:16 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 05:23:16 - forex_backtest.data.data_loader - INFO - Downloading AUDNZD data from 2023-01-01 to 2024-01-01 (5m)
2025-07-11 05:23:17 - yfinance - ERROR - $AUDNZD=X: possibly delisted; no price data found  (5m 2023-01-01 -> 2024-01-01) (Yahoo error = "5m data not available for startTime=1672531200 and endTime=1704067200. The requested range must be within the last 60 days.")
2025-07-11 05:23:17 - root - ERROR - Backtest execution failed: Failed to load data from yfinance: No data returned for AUDNZD
2025-07-11 05:23:17 - __main__ - ERROR - Backtest failed: Failed to load data from yfinance: No data returned for AUDNZD
2025-07-11 05:24:38 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 05:25:10 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 05:25:10 - forex_backtest.data.local_loader - INFO - Loading local data from data/local/AUDNZD_5m.csv
2025-07-11 05:25:10 - forex_backtest.data.local_loader - INFO - Processed data: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 05:25:10 - forex_backtest.data.local_loader - ERROR - Error filtering date range: Invalid comparison between dtype=datetime64[ns, UTC+07:00] and Timestamp
2025-07-11 05:25:10 - forex_backtest.data.market_data - INFO - Market data validated: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 05:25:10 - forex_backtest.data.local_loader - INFO - Loaded 20114 bars from local file
2025-07-11 05:25:10 - forex_backtest.backtesting.backtest_engine - INFO - Starting backtest...
2025-07-11 05:25:10 - forex_backtest.backtesting.backtest_engine - INFO - Generating trading signals...
2025-07-11 05:25:10 - forex_backtest.backtesting.backtest_engine - INFO - Running backtest on 20114 bars...
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Backtest completed successfully
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - === BACKTEST SUMMARY ===
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Initial Balance: $10,000.00
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Final Equity: $10,000.00
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Total Return: 0.00%
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Max Drawdown: 0.00%
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Sharpe Ratio: 0.00
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Total Trades: 0
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Win Rate: 0.0%
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - Profit Factor: 0.00
2025-07-11 05:25:11 - forex_backtest.backtesting.backtest_engine - INFO - ========================
2025-07-11 05:25:12 - root - ERROR - Output generation failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 05:25:12 - __main__ - ERROR - Backtest failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 05:36:40 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 05:36:40 - forex_backtest.data.local_loader - INFO - Loading local data from data/local/AUDNZD_5m.csv
2025-07-11 05:36:40 - forex_backtest.data.local_loader - INFO - Processed data: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 05:36:40 - forex_backtest.data.local_loader - ERROR - Error filtering date range: Invalid comparison between dtype=datetime64[ns, UTC+07:00] and Timestamp
2025-07-11 05:36:40 - forex_backtest.data.market_data - INFO - Market data validated: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 05:36:40 - forex_backtest.data.local_loader - INFO - Loaded 20114 bars from local file
2025-07-11 05:36:40 - forex_backtest.backtesting.backtest_engine - INFO - Starting backtest...
2025-07-11 05:36:40 - forex_backtest.backtesting.backtest_engine - INFO - Generating trading signals...
2025-07-11 05:36:40 - forex_backtest.backtesting.backtest_engine - INFO - Running backtest on 20114 bars...
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Backtest completed successfully
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - === BACKTEST SUMMARY ===
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Initial Balance: $10,000.00
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Final Equity: $10,000.00
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Total Return: 0.00%
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Max Drawdown: 0.00%
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Sharpe Ratio: 0.00
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Total Trades: 0
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Win Rate: 0.0%
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - Profit Factor: 0.00
2025-07-11 05:36:41 - forex_backtest.backtesting.backtest_engine - INFO - ========================
2025-07-11 05:36:41 - root - ERROR - Output generation failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 05:36:41 - __main__ - ERROR - Backtest failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:00:20 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 06:00:20 - forex_backtest.data.local_loader - INFO - Loading local data from data/local/AUDNZD_5m.csv
2025-07-11 06:00:20 - forex_backtest.data.local_loader - INFO - Processed data: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 06:00:20 - forex_backtest.data.local_loader - ERROR - Error filtering date range: Invalid comparison between dtype=datetime64[ns, UTC+07:00] and Timestamp
2025-07-11 06:00:20 - forex_backtest.data.market_data - INFO - Market data validated: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 06:00:20 - forex_backtest.data.local_loader - INFO - Loaded 20114 bars from local file
2025-07-11 06:00:20 - forex_backtest.backtesting.backtest_engine - INFO - Starting backtest...
2025-07-11 06:00:20 - forex_backtest.backtesting.backtest_engine - INFO - Generating trading signals...
2025-07-11 06:00:20 - forex_backtest.backtesting.backtest_engine - INFO - Running backtest on 20114 bars...
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Backtest completed successfully
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - === BACKTEST SUMMARY ===
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Initial Balance: $10,000.00
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Final Equity: $10,000.00
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Total Return: 0.00%
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Max Drawdown: 0.00%
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Sharpe Ratio: 0.00
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Total Trades: 0
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Win Rate: 0.0%
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - Profit Factor: 0.00
2025-07-11 06:00:21 - forex_backtest.backtesting.backtest_engine - INFO - ========================
2025-07-11 06:00:22 - root - ERROR - Output generation failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:00:22 - __main__ - ERROR - Backtest failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:00:53 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 06:00:53 - forex_backtest.data.local_loader - INFO - Loading local data from data/local/AUDNZD_5m.csv
2025-07-11 06:00:53 - forex_backtest.data.local_loader - INFO - Processed data: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 06:00:53 - forex_backtest.data.local_loader - INFO - Filtered data from 2025-04-01 to 2025-06-01: 12678 bars
2025-07-11 06:00:53 - forex_backtest.data.market_data - INFO - Market data validated: 12678 bars from 2025-04-01 00:00:00+07:00 to 2025-05-31 03:55:00+07:00
2025-07-11 06:00:53 - forex_backtest.data.local_loader - INFO - Loaded 12678 bars from local file
2025-07-11 06:00:53 - forex_backtest.backtesting.backtest_engine - INFO - Starting backtest...
2025-07-11 06:00:53 - forex_backtest.backtesting.backtest_engine - INFO - Generating trading signals...
2025-07-11 06:00:53 - forex_backtest.backtesting.backtest_engine - INFO - Running backtest on 12678 bars...
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Backtest completed successfully
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - === BACKTEST SUMMARY ===
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Initial Balance: $10,000.00
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Final Equity: $10,000.00
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Total Return: 0.00%
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Max Drawdown: 0.00%
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Sharpe Ratio: 0.00
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Total Trades: 0
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Win Rate: 0.0%
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - Profit Factor: 0.00
2025-07-11 06:00:54 - forex_backtest.backtesting.backtest_engine - INFO - ========================
2025-07-11 06:00:54 - root - ERROR - Output generation failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:00:54 - __main__ - ERROR - Backtest failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:05:52 - forex_backtest.config.settings - INFO - Configuration loaded successfully from config/settings.yaml
2025-07-11 06:05:52 - forex_backtest.data.local_loader - INFO - Loading local data from data/local/AUDNZD_5m.csv
2025-07-11 06:05:52 - forex_backtest.data.local_loader - INFO - Processed data: 20114 bars from 2025-03-24 04:00:00+07:00 to 2025-06-28 03:55:00+07:00
2025-07-11 06:05:52 - forex_backtest.data.local_loader - INFO - Filtered data from 2025-04-01 to 2025-06-01: 12678 bars
2025-07-11 06:05:52 - forex_backtest.data.market_data - INFO - Market data validated: 12678 bars from 2025-04-01 00:00:00+07:00 to 2025-05-31 03:55:00+07:00
2025-07-11 06:05:52 - forex_backtest.data.local_loader - INFO - Loaded 12678 bars from local file
2025-07-11 06:05:52 - forex_backtest.backtesting.backtest_engine - INFO - Starting backtest...
2025-07-11 06:05:52 - forex_backtest.backtesting.backtest_engine - INFO - Generating trading signals...
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Generating signals for 12678 bars of AUDNZD data
2025-07-11 06:05:52 - forex_backtest.indicators.signals - INFO - Crossover signals - Raw: 267, Filtered: 17, Confirmed: 17
2025-07-11 06:05:52 - forex_backtest.indicators.signals - INFO - Final trading signals after all filters: 0
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Generated 0 raw trading signals
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Final strategy signals after filtering: 0
2025-07-11 06:05:52 - forex_backtest.backtesting.backtest_engine - INFO - Running backtest on 12678 bars...
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 0 at 2025-04-01 00:00:00+07:00
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 1000 at 2025-04-04 11:30:00+07:00
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 2000 at 2025-04-09 23:20:00+07:00
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 3000 at 2025-04-15 10:45:00+07:00
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 4000 at 2025-04-18 22:15:00+07:00
2025-07-11 06:05:52 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 5000 at 2025-04-24 10:35:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 6000 at 2025-04-29 22:15:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 7000 at 2025-05-05 10:20:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 8000 at 2025-05-08 21:40:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 9000 at 2025-05-14 09:00:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 10000 at 2025-05-19 20:30:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 11000 at 2025-05-23 08:00:00+07:00
2025-07-11 06:05:53 - forex_backtest.strategy.ema_cross_strategy - INFO - Processing bar 12000 at 2025-05-28 19:20:00+07:00
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Backtest completed successfully
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - === BACKTEST SUMMARY ===
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Initial Balance: $10,000.00
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Final Equity: $10,000.00
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Total Return: 0.00%
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Max Drawdown: 0.00%
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Sharpe Ratio: 0.00
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Total Trades: 0
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Win Rate: 0.0%
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - Profit Factor: 0.00
2025-07-11 06:05:53 - forex_backtest.backtesting.backtest_engine - INFO - ========================
2025-07-11 06:05:53 - root - ERROR - Output generation failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

2025-07-11 06:05:53 - __main__ - ERROR - Backtest failed: 
Subplot with type '{subplot_type}' at grid position ({row}, {col}) was not
created with the secondary_y spec property set to True. See the docstring
for the specs argument to plotly.subplots.make_subplots for more information.

