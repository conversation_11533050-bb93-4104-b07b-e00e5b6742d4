#!/usr/bin/env python3
"""
Debug script to analyze AUDNZD signal generation issues.
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from forex_backtest import load_config, DataLoader
from forex_backtest.strategy.ema_cross_strategy import EMACrossStrategy
from forex_backtest.indicators.signals import SignalGenerator
from forex_backtest.indicators.ema import calculate_ema, ema_crossover_signals


def analyze_data_requirements():
    """Analyze data requirements for EMA indicators."""
    print("🔍 ANALYZING DATA REQUIREMENTS")
    print("=" * 50)
    
    # Load configuration
    config = load_config('config/settings.yaml')
    
    # Get EMA periods
    ema_fast = config['strategy']['ema_fast']
    ema_slow = config['strategy']['ema_slow']
    
    print(f"EMA Fast Period: {ema_fast}")
    print(f"EMA Slow Period: {ema_slow}")
    print(f"Minimum data required: {ema_slow} bars")
    print(f"Recommended data: {ema_slow * 3} bars for proper warm-up")
    
    return ema_fast, ema_slow


def test_data_loading():
    """Test AUDNZD data loading with different configurations."""
    print("\n🔍 TESTING DATA LOADING")
    print("=" * 50)
    
    # Test configurations
    test_configs = [
        {
            'name': 'Current Config (2025-04-01 to 2025-06-01, 5m)',
            'config': {
                'data': {
                    'source': 'yfinance',
                    'start_date': '2025-04-01',
                    'end_date': '2025-06-01'
                }
            },
            'timeframe': '5m'
        },
        {
            'name': 'Extended Period (2024-01-01 to 2024-12-31, 1d)',
            'config': {
                'data': {
                    'source': 'yfinance',
                    'start_date': '2024-01-01',
                    'end_date': '2024-12-31'
                }
            },
            'timeframe': '1d'
        },
        {
            'name': 'Medium Period (2024-06-01 to 2024-12-31, 4h)',
            'config': {
                'data': {
                    'source': 'yfinance',
                    'start_date': '2024-06-01',
                    'end_date': '2024-12-31'
                }
            },
            'timeframe': '4h'
        }
    ]
    
    results = []
    
    for test_config in test_configs:
        print(f"\n📊 Testing: {test_config['name']}")
        try:
            data_loader = DataLoader(test_config['config'])
            market_data = data_loader.load_data(
                'AUDNZD',
                test_config['config']['data']['start_date'],
                test_config['config']['data']['end_date'],
                test_config['timeframe']
            )
            
            data_length = len(market_data.data)
            print(f"   ✅ Data loaded: {data_length} bars")
            print(f"   Date range: {market_data.data.index[0]} to {market_data.data.index[-1]}")
            
            results.append({
                'name': test_config['name'],
                'bars': data_length,
                'data': market_data.data,
                'success': True
            })
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            results.append({
                'name': test_config['name'],
                'bars': 0,
                'data': None,
                'success': False,
                'error': str(e)
            })
    
    return results


def analyze_ema_calculation(data, ema_fast, ema_slow):
    """Analyze EMA calculation and crossover signals."""
    print(f"\n🔍 ANALYZING EMA CALCULATION ({len(data)} bars)")
    print("=" * 50)
    
    # Calculate EMAs
    fast_ema = calculate_ema(data, ema_fast, 'Close')
    slow_ema = calculate_ema(data, ema_slow, 'Close')
    
    # Count valid EMA values
    fast_valid = fast_ema.notna().sum()
    slow_valid = slow_ema.notna().sum()
    
    print(f"Fast EMA ({ema_fast}): {fast_valid} valid values out of {len(data)}")
    print(f"Slow EMA ({ema_slow}): {slow_valid} valid values out of {len(data)}")
    
    if slow_valid == 0:
        print("❌ No valid slow EMA values - insufficient data!")
        return None, None, None
    
    # Calculate crossover signals
    raw_signals = ema_crossover_signals(fast_ema, slow_ema)
    signal_count = (raw_signals != 0).sum()
    
    print(f"Raw crossover signals: {signal_count}")
    
    # Show signal details
    if signal_count > 0:
        signal_dates = data.index[raw_signals != 0]
        signal_values = raw_signals[raw_signals != 0]
        
        print("Signal details:")
        for date, signal in zip(signal_dates, signal_values):
            signal_type = "BUY" if signal > 0 else "SELL"
            print(f"   {date}: {signal_type} signal")
    
    return fast_ema, slow_ema, raw_signals


def analyze_signal_filtering(data, config):
    """Analyze signal filtering and validation."""
    print(f"\n🔍 ANALYZING SIGNAL FILTERING")
    print("=" * 50)
    
    # Create signal generator
    signal_generator = SignalGenerator(config)
    
    # Generate all signals
    signals_df = signal_generator.generate_all_signals(data)
    
    # Analyze signal counts at each stage
    raw_signals = (signals_df['Raw_Signal'] != 0).sum()
    filtered_signals = (signals_df['Filtered_Signal'] != 0).sum()
    confirmed_signals = (signals_df['Confirmed_Signal'] != 0).sum()
    trading_signals = (signals_df['Trading_Signal'] != 0).sum()
    
    print(f"Signal progression:")
    print(f"   Raw signals: {raw_signals}")
    print(f"   After anti-whipsaw filter: {filtered_signals}")
    print(f"   After confirmation filter: {confirmed_signals}")
    print(f"   Final trading signals: {trading_signals}")
    
    # Analyze filtering reasons
    if raw_signals > 0 and trading_signals == 0:
        print("\n❌ Signals are being filtered out!")
        
        # Check signal strength
        signal_strengths = signals_df['Signal_Strength'].value_counts()
        print(f"Signal strength distribution: {dict(signal_strengths)}")
        
        # Check volatility regime
        volatility_regimes = signals_df['Volatility_Regime'].value_counts()
        print(f"Volatility regime distribution: {dict(volatility_regimes)}")
        
        # Check minimum separation
        min_separation = config['strategy'].get('min_ema_separation', 0.0001)
        ema_separation = abs(signals_df['Fast_EMA'] - signals_df['Slow_EMA'])
        separation_stats = ema_separation.describe()
        print(f"EMA separation stats (min required: {min_separation}):")
        print(f"   Mean: {separation_stats['mean']:.6f}")
        print(f"   Min: {separation_stats['min']:.6f}")
        print(f"   Max: {separation_stats['max']:.6f}")
    
    return signals_df


def test_strategy_with_different_parameters():
    """Test strategy with different parameter configurations."""
    print(f"\n🔍 TESTING DIFFERENT STRATEGY PARAMETERS")
    print("=" * 50)
    
    # Load base configuration
    base_config = load_config('config/settings.yaml')
    
    # Test different EMA combinations
    test_params = [
        {'ema_fast': 21, 'ema_slow': 55, 'name': 'Shorter EMAs (21/55)'},
        {'ema_fast': 12, 'ema_slow': 26, 'name': 'Much Shorter EMAs (12/26)'},
        {'ema_fast': 8, 'ema_slow': 21, 'name': 'Very Short EMAs (8/21)'},
        {'ema_fast': 55, 'ema_slow': 89, 'name': 'Original EMAs (55/89)'},
    ]
    
    # Use daily data for testing
    try:
        data_loader = DataLoader({
            'data': {
                'source': 'yfinance',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31'
            }
        })
        market_data = data_loader.load_data('AUDNZD', '2024-01-01', '2024-12-31', '1d')
        data = market_data.data
        
        print(f"Using {len(data)} bars of daily AUDNZD data")
        
        for params in test_params:
            print(f"\n📊 Testing {params['name']}")
            
            # Update configuration
            test_config = base_config.copy()
            test_config['strategy']['ema_fast'] = params['ema_fast']
            test_config['strategy']['ema_slow'] = params['ema_slow']
            
            # Analyze EMAs
            fast_ema, slow_ema, raw_signals = analyze_ema_calculation(
                data, params['ema_fast'], params['ema_slow']
            )
            
            if raw_signals is not None:
                signal_count = (raw_signals != 0).sum()
                print(f"   Raw signals generated: {signal_count}")
                
                if signal_count > 0:
                    # Test full signal filtering
                    signals_df = analyze_signal_filtering(data, test_config)
                    final_signals = (signals_df['Trading_Signal'] != 0).sum()
                    print(f"   Final trading signals: {final_signals}")
    
    except Exception as e:
        print(f"❌ Error testing parameters: {e}")


def main():
    """Run comprehensive signal generation analysis."""
    print("🚀 AUDNZD SIGNAL GENERATION DIAGNOSTIC")
    print("=" * 60)
    
    # Step 1: Analyze data requirements
    ema_fast, ema_slow = analyze_data_requirements()
    
    # Step 2: Test data loading
    data_results = test_data_loading()
    
    # Step 3: Find best data configuration
    best_result = None
    for result in data_results:
        if result['success'] and result['bars'] >= ema_slow:
            best_result = result
            break
    
    if best_result:
        print(f"\n✅ Using {best_result['name']} for detailed analysis")
        
        # Step 4: Analyze EMA calculation
        fast_ema, slow_ema, raw_signals = analyze_ema_calculation(
            best_result['data'], ema_fast, ema_slow
        )
        
        if raw_signals is not None:
            # Step 5: Analyze signal filtering
            config = load_config('config/settings.yaml')
            signals_df = analyze_signal_filtering(best_result['data'], config)
    
    # Step 6: Test different parameters
    test_strategy_with_different_parameters()
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print("=" * 50)
    print("1. Use longer date ranges (6-12 months) for adequate data")
    print("2. Consider daily or 4-hour timeframes for more reliable signals")
    print("3. Try shorter EMA periods (21/55 or 12/26) for more frequent signals")
    print("4. Reduce minimum EMA separation requirement")
    print("5. Lower signal strength requirements for cross pairs")


if __name__ == '__main__':
    main()
