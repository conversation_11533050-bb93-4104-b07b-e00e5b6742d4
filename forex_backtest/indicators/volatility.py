"""
Volatility indicators including Average True Range (ATR) for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Union, Optional

logger = logging.getLogger(__name__)


def calculate_true_range(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """
    Calculate True Range for each period.
    
    True Range = max(
        High - Low,
        abs(High - Previous Close),
        abs(Low - Previous Close)
    )
    
    Args:
        high: High prices
        low: Low prices
        close: Close prices
        
    Returns:
        True Range series
    """
    prev_close = close.shift(1)
    
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # First value uses high - low since there's no previous close
    true_range.iloc[0] = high.iloc[0] - low.iloc[0]
    
    return true_range


def calculate_atr(data: Union[pd.DataFrame, dict], period: int = 14) -> pd.Series:
    """
    Calculate Average True Range (ATR).
    
    Args:
        data: OHLC data (DataFrame with High, Low, Close columns or dict)
        period: ATR period (default: 14)
        
    Returns:
        ATR values as pandas Series
        
    Raises:
        ValueError: If invalid parameters or missing data
    """
    if period <= 0:
        raise ValueError("ATR period must be positive")
    
    # Handle different input types
    if isinstance(data, dict):
        high = data['High']
        low = data['Low']
        close = data['Close']
    elif isinstance(data, pd.DataFrame):
        required_cols = ['High', 'Low', 'Close']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        high = data['High']
        low = data['Low']
        close = data['Close']
    else:
        raise ValueError("Data must be DataFrame or dict with High, Low, Close")
    
    if len(high) < period:
        logger.warning(f"Data length ({len(high)}) is less than ATR period ({period})")
    
    # Calculate True Range
    true_range = calculate_true_range(high, low, close)
    
    # Calculate ATR using exponential moving average
    atr = true_range.ewm(span=period, adjust=False).mean()
    
    # Set initial values to NaN for proper warm-up
    atr.iloc[:period-1] = np.nan
    
    return atr


def calculate_atr_sma(data: Union[pd.DataFrame, dict], period: int = 14) -> pd.Series:
    """
    Calculate ATR using Simple Moving Average (traditional method).
    
    Args:
        data: OHLC data
        period: ATR period
        
    Returns:
        ATR values using SMA
    """
    if isinstance(data, dict):
        high = data['High']
        low = data['Low']
        close = data['Close']
    else:
        high = data['High']
        low = data['Low']
        close = data['Close']
    
    true_range = calculate_true_range(high, low, close)
    atr = true_range.rolling(window=period).mean()
    
    return atr


class ATRIndicator:
    """
    ATR indicator class with additional functionality.
    """
    
    def __init__(self, period: int = 14, method: str = 'ema'):
        """
        Initialize ATR indicator.
        
        Args:
            period: ATR period
            method: Calculation method ('ema' or 'sma')
        """
        self.period = period
        self.method = method
        self._atr_values = None
        self._true_ranges = []
        self._last_atr = None
        
    def calculate(self, data: Union[pd.DataFrame, dict]) -> pd.Series:
        """
        Calculate ATR for given data.
        
        Args:
            data: OHLC data
            
        Returns:
            ATR values
        """
        if self.method == 'ema':
            self._atr_values = calculate_atr(data, self.period)
        else:
            self._atr_values = calculate_atr_sma(data, self.period)
        
        return self._atr_values
    
    def update(self, high: float, low: float, close: float, prev_close: float) -> float:
        """
        Update ATR with new OHLC values (for real-time calculation).
        
        Args:
            high: Current high
            low: Current low
            close: Current close
            prev_close: Previous close
            
        Returns:
            Updated ATR value
        """
        # Calculate true range for current period
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        current_tr = max(tr1, tr2, tr3)
        
        self._true_ranges.append(current_tr)
        
        # Keep only the required number of periods
        if len(self._true_ranges) > self.period:
            self._true_ranges.pop(0)
        
        # Calculate ATR
        if len(self._true_ranges) < self.period:
            # Not enough data yet
            self._last_atr = sum(self._true_ranges) / len(self._true_ranges)
        else:
            if self.method == 'ema':
                # EMA method
                if self._last_atr is None:
                    self._last_atr = sum(self._true_ranges) / self.period
                else:
                    multiplier = 2.0 / (self.period + 1)
                    self._last_atr = (current_tr - self._last_atr) * multiplier + self._last_atr
            else:
                # SMA method
                self._last_atr = sum(self._true_ranges) / self.period
        
        return self._last_atr
    
    def get_current_value(self) -> Optional[float]:
        """Get the current ATR value."""
        return self._last_atr
    
    def is_ready(self, data_length: int) -> bool:
        """Check if ATR has enough data for reliable calculation."""
        return data_length >= self.period
    
    def get_warmup_period(self) -> int:
        """Get the number of periods needed for ATR warm-up."""
        return self.period
    
    def reset(self) -> None:
        """Reset the indicator state."""
        self._atr_values = None
        self._true_ranges = []
        self._last_atr = None


def calculate_volatility_bands(close: pd.Series, atr: pd.Series, 
                              multiplier: float = 2.0) -> pd.DataFrame:
    """
    Calculate volatility bands based on ATR.
    
    Args:
        close: Close prices
        atr: ATR values
        multiplier: ATR multiplier for bands
        
    Returns:
        DataFrame with upper and lower bands
    """
    bands = pd.DataFrame(index=close.index)
    bands['Upper'] = close + (atr * multiplier)
    bands['Lower'] = close - (atr * multiplier)
    bands['Middle'] = close
    
    return bands


def calculate_atr_percent(close: pd.Series, atr: pd.Series) -> pd.Series:
    """
    Calculate ATR as percentage of close price.
    
    Args:
        close: Close prices
        atr: ATR values
        
    Returns:
        ATR percentage series
    """
    return (atr / close) * 100


def atr_position_sizing(balance: float, risk_percent: float, 
                       atr: float, pip_value: float) -> float:
    """
    Calculate position size based on ATR and risk management.
    
    Args:
        balance: Account balance
        risk_percent: Risk percentage (e.g., 0.02 for 2%)
        atr: Current ATR value
        pip_value: Pip value for the symbol
        
    Returns:
        Position size in lots
    """
    risk_amount = balance * risk_percent
    atr_pips = atr / pip_value
    
    if atr_pips == 0:
        return 0
    
    position_size = risk_amount / atr_pips
    return position_size


def atr_stop_loss_distance(atr: float, multiplier: float = 2.0) -> float:
    """
    Calculate stop loss distance based on ATR.
    
    Args:
        atr: Current ATR value
        multiplier: ATR multiplier
        
    Returns:
        Stop loss distance
    """
    return atr * multiplier


def atr_profit_target_distance(atr: float, multiplier: float = 3.0) -> float:
    """
    Calculate profit target distance based on ATR.
    
    Args:
        atr: Current ATR value
        multiplier: ATR multiplier
        
    Returns:
        Profit target distance
    """
    return atr * multiplier
