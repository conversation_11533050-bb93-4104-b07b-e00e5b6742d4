"""
Exponential Moving Average (EMA) calculations for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Optional, Union

logger = logging.getLogger(__name__)


def calculate_ema(data: Union[pd.Series, pd.DataFrame], period: int, 
                 column: str = 'Close') -> pd.Series:
    """
    Calculate Exponential Moving Average.
    
    Args:
        data: Price data (Series or DataFrame)
        period: EMA period
        column: Column name if DataFrame is provided
        
    Returns:
        EMA values as pandas Series
        
    Raises:
        ValueError: If invalid parameters provided
    """
    if period <= 0:
        raise ValueError("EMA period must be positive")
    
    if isinstance(data, pd.DataFrame):
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        prices = data[column]
    else:
        prices = data
    
    if len(prices) < period:
        logger.warning(f"Data length ({len(prices)}) is less than EMA period ({period})")
    
    # Calculate EMA using pandas ewm
    ema = prices.ewm(span=period, adjust=False).mean()
    
    # Set initial values to NaN for proper warm-up
    ema.iloc[:period-1] = np.nan
    
    return ema


def calculate_ema_manual(prices: pd.Series, period: int) -> pd.Series:
    """
    Calculate EMA manually using the traditional formula.
    EMA = (Close - EMA_prev) * multiplier + EMA_prev
    where multiplier = 2 / (period + 1)
    
    Args:
        prices: Price series
        period: EMA period
        
    Returns:
        EMA values as pandas Series
    """
    if period <= 0:
        raise ValueError("EMA period must be positive")
    
    multiplier = 2.0 / (period + 1)
    ema = pd.Series(index=prices.index, dtype=float)
    
    # Initialize first EMA value with SMA
    if len(prices) >= period:
        ema.iloc[period-1] = prices.iloc[:period].mean()
        
        # Calculate subsequent EMA values
        for i in range(period, len(prices)):
            ema.iloc[i] = (prices.iloc[i] - ema.iloc[i-1]) * multiplier + ema.iloc[i-1]
    
    return ema


class EMAIndicator:
    """
    EMA indicator class with additional functionality.
    """
    
    def __init__(self, period: int, column: str = 'Close'):
        """
        Initialize EMA indicator.
        
        Args:
            period: EMA period
            column: Column name for calculation
        """
        self.period = period
        self.column = column
        self._ema_values = None
        self._last_price = None
        self._last_ema = None
        
    def calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        Calculate EMA for given data.
        
        Args:
            data: Price data
            
        Returns:
            EMA values
        """
        self._ema_values = calculate_ema(data, self.period, self.column)
        return self._ema_values
    
    def update(self, new_price: float) -> float:
        """
        Update EMA with new price (for real-time calculation).
        
        Args:
            new_price: New price value
            
        Returns:
            Updated EMA value
        """
        if self._last_ema is None:
            # First update, return the price itself
            self._last_ema = new_price
            self._last_price = new_price
            return new_price
        
        multiplier = 2.0 / (self.period + 1)
        new_ema = (new_price - self._last_ema) * multiplier + self._last_ema
        
        self._last_ema = new_ema
        self._last_price = new_price
        
        return new_ema
    
    def get_current_value(self) -> Optional[float]:
        """Get the current EMA value."""
        return self._last_ema
    
    def is_ready(self, data_length: int) -> bool:
        """Check if EMA has enough data for reliable calculation."""
        return data_length >= self.period
    
    def get_warmup_period(self) -> int:
        """Get the number of periods needed for EMA warm-up."""
        return self.period
    
    def reset(self) -> None:
        """Reset the indicator state."""
        self._ema_values = None
        self._last_price = None
        self._last_ema = None


def calculate_multiple_emas(data: Union[pd.Series, pd.DataFrame], 
                           periods: list, column: str = 'Close') -> pd.DataFrame:
    """
    Calculate multiple EMAs at once.
    
    Args:
        data: Price data
        periods: List of EMA periods
        column: Column name if DataFrame is provided
        
    Returns:
        DataFrame with EMA columns
    """
    if isinstance(data, pd.DataFrame):
        prices = data[column]
    else:
        prices = data
    
    ema_df = pd.DataFrame(index=prices.index)
    
    for period in periods:
        ema_df[f'EMA_{period}'] = calculate_ema(prices, period)
    
    return ema_df


def ema_crossover_signals(fast_ema: pd.Series, slow_ema: pd.Series) -> pd.Series:
    """
    Generate crossover signals from two EMA series.
    
    Args:
        fast_ema: Fast EMA series
        slow_ema: Slow EMA series
        
    Returns:
        Signal series: 1 for bullish crossover, -1 for bearish crossover, 0 otherwise
    """
    # Calculate crossover points
    fast_above_slow = fast_ema > slow_ema
    crossover = fast_above_slow.astype(int).diff()
    
    # Generate signals
    signals = pd.Series(0, index=fast_ema.index)
    signals[crossover == 1] = 1   # Bullish crossover (fast crosses above slow)
    signals[crossover == -1] = -1  # Bearish crossover (fast crosses below slow)
    
    return signals


def ema_trend_direction(fast_ema: pd.Series, slow_ema: pd.Series) -> pd.Series:
    """
    Determine trend direction based on EMA relationship.
    
    Args:
        fast_ema: Fast EMA series
        slow_ema: Slow EMA series
        
    Returns:
        Trend series: 1 for uptrend, -1 for downtrend, 0 for sideways
    """
    trend = pd.Series(0, index=fast_ema.index)
    trend[fast_ema > slow_ema] = 1   # Uptrend
    trend[fast_ema < slow_ema] = -1  # Downtrend
    
    return trend


def ema_distance(fast_ema: pd.Series, slow_ema: pd.Series, 
                normalize: bool = True) -> pd.Series:
    """
    Calculate distance between two EMAs.
    
    Args:
        fast_ema: Fast EMA series
        slow_ema: Slow EMA series
        normalize: Whether to normalize by slow EMA (percentage distance)
        
    Returns:
        Distance series
    """
    distance = fast_ema - slow_ema
    
    if normalize:
        distance = distance / slow_ema * 100  # Percentage distance
    
    return distance
