"""
Signal generation logic for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple, List
from enum import Enum

from .ema import calculate_ema, ema_crossover_signals
from .volatility import calculate_atr

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """Signal types enumeration."""
    LONG = 1
    SHORT = -1
    NEUTRAL = 0


class SignalStrength(Enum):
    """Signal strength enumeration."""
    WEAK = 1
    MEDIUM = 2
    STRONG = 3


class CrossoverSignal:
    """
    EMA crossover signal generator with anti-whipsaw protection.
    """
    
    def __init__(self, fast_period: int = 55, slow_period: int = 89, 
                 min_separation: float = 0.0001, confirmation_bars: int = 1):
        """
        Initialize crossover signal generator.
        
        Args:
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            min_separation: Minimum separation between EMAs for valid signal
            confirmation_bars: Number of bars to confirm signal
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.min_separation = min_separation
        self.confirmation_bars = confirmation_bars
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate crossover signals from OHLC data.
        
        Args:
            data: OHLC DataFrame
            
        Returns:
            DataFrame with signal information
        """
        # Calculate EMAs
        fast_ema = calculate_ema(data, self.fast_period, 'Close')
        slow_ema = calculate_ema(data, self.slow_period, 'Close')
        
        # Calculate basic crossover signals
        raw_signals = ema_crossover_signals(fast_ema, slow_ema)
        
        # Apply anti-whipsaw filter
        filtered_signals = self._apply_anti_whipsaw_filter(
            raw_signals, fast_ema, slow_ema, data['Close']
        )
        
        # Apply confirmation filter
        confirmed_signals = self._apply_confirmation_filter(filtered_signals)
        
        # Create signal DataFrame
        signals_df = pd.DataFrame(index=data.index)
        signals_df['Fast_EMA'] = fast_ema
        signals_df['Slow_EMA'] = slow_ema
        signals_df['Raw_Signal'] = raw_signals
        signals_df['Filtered_Signal'] = filtered_signals
        signals_df['Confirmed_Signal'] = confirmed_signals
        signals_df['Signal_Strength'] = self._calculate_signal_strength(
            fast_ema, slow_ema, data
        )
        
        return signals_df
    
    def _apply_anti_whipsaw_filter(self, signals: pd.Series, fast_ema: pd.Series,
                                  slow_ema: pd.Series, close: pd.Series) -> pd.Series:
        """Apply anti-whipsaw filter to reduce false signals."""
        filtered_signals = signals.copy()
        
        for i in range(len(signals)):
            if signals.iloc[i] != 0:  # Signal detected
                # Check minimum separation
                ema_separation = abs(fast_ema.iloc[i] - slow_ema.iloc[i])
                if ema_separation < self.min_separation:
                    filtered_signals.iloc[i] = 0
                    continue
                
                # Check if EMAs are diverging (not converging)
                if i > 0:
                    prev_separation = abs(fast_ema.iloc[i-1] - slow_ema.iloc[i-1])
                    if ema_separation <= prev_separation:
                        # EMAs are converging, might be false signal
                        filtered_signals.iloc[i] = 0
        
        return filtered_signals
    
    def _apply_confirmation_filter(self, signals: pd.Series) -> pd.Series:
        """Apply confirmation filter requiring signal persistence."""
        if self.confirmation_bars <= 1:
            return signals
        
        confirmed_signals = pd.Series(0, index=signals.index)
        
        for i in range(self.confirmation_bars, len(signals)):
            if signals.iloc[i] != 0:  # Signal detected
                # Check if signal persists for confirmation_bars
                signal_type = signals.iloc[i]
                confirmed = True
                
                for j in range(1, self.confirmation_bars):
                    if i - j < 0:
                        confirmed = False
                        break
                    
                    # For confirmation, we check if the trend continues
                    # (not necessarily the same signal value)
                    if signal_type > 0 and signals.iloc[i-j] < 0:
                        confirmed = False
                        break
                    elif signal_type < 0 and signals.iloc[i-j] > 0:
                        confirmed = False
                        break
                
                if confirmed:
                    confirmed_signals.iloc[i] = signal_type
        
        return confirmed_signals
    
    def _calculate_signal_strength(self, fast_ema: pd.Series, slow_ema: pd.Series,
                                  data: pd.DataFrame) -> pd.Series:
        """Calculate signal strength based on various factors."""
        strength = pd.Series(SignalStrength.WEAK.value, index=data.index)
        
        # Calculate ATR for volatility context
        atr = calculate_atr(data, 14)
        
        for i in range(len(data)):
            if i < 14:  # Not enough data for ATR
                continue
            
            ema_separation = abs(fast_ema.iloc[i] - slow_ema.iloc[i])
            atr_value = atr.iloc[i]
            
            if pd.isna(atr_value) or atr_value == 0:
                continue
            
            # Normalize separation by ATR
            normalized_separation = ema_separation / atr_value
            
            if normalized_separation > 2.0:
                strength.iloc[i] = SignalStrength.STRONG.value
            elif normalized_separation > 1.0:
                strength.iloc[i] = SignalStrength.MEDIUM.value
        
        return strength


class SignalGenerator:
    """
    Main signal generator class combining multiple signal types.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize signal generator with configuration.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.strategy_config = config.get('strategy', {})
        
        # Initialize crossover signal generator
        self.crossover_generator = CrossoverSignal(
            fast_period=self.strategy_config.get('ema_fast', 55),
            slow_period=self.strategy_config.get('ema_slow', 89),
            min_separation=self.strategy_config.get('min_ema_separation', 0.0001),
            confirmation_bars=self.strategy_config.get('confirmation_bars', 1)
        )
        
    def generate_all_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate all types of signals for the given data."""
        # Generate crossover signals
        signals_df = self.crossover_generator.generate_signals(data)
        
        # Log raw crossover signals
        raw_signal_count = (signals_df['Raw_Signal'] != 0).sum()
        filtered_signal_count = (signals_df['Filtered_Signal'] != 0).sum()
        confirmed_signal_count = (signals_df['Confirmed_Signal'] != 0).sum()
        
        logger.info(f"Crossover signals - Raw: {raw_signal_count}, Filtered: {filtered_signal_count}, Confirmed: {confirmed_signal_count}")
        
        # Add ATR for context
        signals_df['ATR'] = calculate_atr(data, 14)
        
        # Add trend context
        signals_df['Trend'] = self._calculate_trend_context(
            signals_df['Fast_EMA'], signals_df['Slow_EMA']
        )
        
        # Add volatility context
        signals_df['Volatility_Regime'] = self._calculate_volatility_regime(
            signals_df['ATR']
        )
        
        # Generate final trading signals
        signals_df['Trading_Signal'] = self._generate_trading_signals(signals_df)
        
        # Log final trading signals
        trading_signal_count = (signals_df['Trading_Signal'] != 0).sum()
        logger.info(f"Final trading signals after all filters: {trading_signal_count}")
        
        return signals_df
    
    def _calculate_trend_context(self, fast_ema: pd.Series, slow_ema: pd.Series) -> pd.Series:
        """Calculate overall trend context."""
        trend = pd.Series(0, index=fast_ema.index)
        trend[fast_ema > slow_ema] = 1   # Uptrend
        trend[fast_ema < slow_ema] = -1  # Downtrend
        
        return trend
    
    def _calculate_volatility_regime(self, atr: pd.Series) -> pd.Series:
        """Calculate volatility regime (low, medium, high)."""
        # Use rolling percentiles to determine volatility regime
        atr_percentile = atr.rolling(window=50).rank(pct=True)
        
        volatility_regime = pd.Series(1, index=atr.index)  # Default: medium
        volatility_regime[atr_percentile < 0.33] = 0  # Low volatility
        volatility_regime[atr_percentile > 0.67] = 2  # High volatility
        
        return volatility_regime
    
    def _generate_trading_signals(self, signals_df: pd.DataFrame) -> pd.Series:
        """Generate final trading signals based on all factors."""
        trading_signals = pd.Series(0, index=signals_df.index)
        
        # Use confirmed signals as base
        base_signals = signals_df['Confirmed_Signal']
        
        for i in range(len(signals_df)):
            if base_signals.iloc[i] != 0:
                signal_strength = signals_df['Signal_Strength'].iloc[i]
                volatility_regime = signals_df['Volatility_Regime'].iloc[i]
                
                # Only trade medium to strong signals in appropriate volatility
                if (signal_strength >= SignalStrength.MEDIUM.value and 
                    volatility_regime != 0):  # Not in low volatility regime
                    trading_signals.iloc[i] = base_signals.iloc[i]
        
        return trading_signals
    
    def get_signal_summary(self, signals_df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics of generated signals."""
        trading_signals = signals_df['Trading_Signal']
        
        total_signals = (trading_signals != 0).sum()
        long_signals = (trading_signals > 0).sum()
        short_signals = (trading_signals < 0).sum()
        
        return {
            'total_signals': total_signals,
            'long_signals': long_signals,
            'short_signals': short_signals,
            'signal_frequency': total_signals / len(signals_df) if len(signals_df) > 0 else 0,
            'long_short_ratio': long_signals / short_signals if short_signals > 0 else float('inf')
        }
