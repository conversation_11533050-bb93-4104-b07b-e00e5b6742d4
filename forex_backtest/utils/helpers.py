"""
Utility helper functions for Forex backtesting system.
"""

import logging
import os
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path
import colorama
from colorama import Fore, Back, Style

# Initialize colorama for cross-platform colored output
colorama.init()


def setup_logging(level: str = "INFO", log_file: Optional[str] = None, 
                 console: bool = True) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Path to log file (optional)
        console: Whether to log to console
        
    Returns:
        Configured logger
    """
    # Create logs directory if needed
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Get root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Add console handler
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # Add file handler
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def format_currency(amount: float, currency: str = "USD", decimals: int = 2) -> str:
    """
    Format currency amount for display.
    
    Args:
        amount: Amount to format
        currency: Currency code
        decimals: Number of decimal places
        
    Returns:
        Formatted currency string
    """
    if currency.upper() == "USD":
        symbol = "$"
    elif currency.upper() == "EUR":
        symbol = "€"
    elif currency.upper() == "GBP":
        symbol = "£"
    elif currency.upper() == "JPY":
        symbol = "¥"
        decimals = 0  # JPY typically doesn't use decimals
    else:
        symbol = currency + " "
    
    return f"{symbol}{amount:,.{decimals}f}"


def calculate_pip_value(symbol: str, account_currency: str = "USD", 
                       lot_size: float = 1.0) -> float:
    """
    Calculate pip value for a given symbol.
    
    Args:
        symbol: Trading symbol (e.g., 'AUDUSD')
        account_currency: Account currency
        lot_size: Lot size
        
    Returns:
        Pip value in account currency
    """
    # Standard pip values for major pairs (per standard lot)
    pip_values = {
        'AUDUSD': 10.0,
        'EURUSD': 10.0,
        'GBPUSD': 10.0,
        'NZDUSD': 10.0,
        'USDCAD': 10.0,
        'USDCHF': 10.0,
        'USDJPY': 10.0,  # Approximate, varies with exchange rate
    }
    
    base_pip_value = pip_values.get(symbol.upper(), 10.0)
    return base_pip_value * lot_size


def validate_timeframe(timeframe: str) -> bool:
    """
    Validate timeframe format.
    
    Args:
        timeframe: Timeframe string (e.g., '5m', '1h', '1d')
        
    Returns:
        True if valid timeframe
    """
    valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
    return timeframe in valid_timeframes


def calculate_position_size_risk(balance: float, risk_percent: float, 
                                stop_loss_pips: float, pip_value: float) -> float:
    """
    Calculate position size based on risk management.
    
    Args:
        balance: Account balance
        risk_percent: Risk percentage (e.g., 0.02 for 2%)
        stop_loss_pips: Stop loss distance in pips
        pip_value: Pip value
        
    Returns:
        Position size in lots
    """
    if stop_loss_pips <= 0 or pip_value <= 0:
        return 0.0
    
    risk_amount = balance * risk_percent
    position_size = risk_amount / (stop_loss_pips * pip_value)
    
    return round(position_size, 2)


def pips_to_price(pips: float, symbol: str) -> float:
    """
    Convert pips to price difference.
    
    Args:
        pips: Number of pips
        symbol: Trading symbol
        
    Returns:
        Price difference
    """
    # Pip sizes for different symbols
    pip_sizes = {
        'USDJPY': 0.01,    # 2 decimal places
        'AUDUSD': 0.0001,  # 4 decimal places
        'EURUSD': 0.0001,
        'GBPUSD': 0.0001,
        'NZDUSD': 0.0001,
        'USDCAD': 0.0001,
        'USDCHF': 0.0001,
    }
    
    pip_size = pip_sizes.get(symbol.upper(), 0.0001)
    return pips * pip_size


def price_to_pips(price_diff: float, symbol: str) -> float:
    """
    Convert price difference to pips.
    
    Args:
        price_diff: Price difference
        symbol: Trading symbol
        
    Returns:
        Number of pips
    """
    pip_size = pips_to_price(1, symbol)
    return price_diff / pip_size


def format_percentage(value: float, decimals: int = 2) -> str:
    """
    Format percentage for display.
    
    Args:
        value: Percentage value
        decimals: Number of decimal places
        
    Returns:
        Formatted percentage string
    """
    color = Fore.GREEN if value >= 0 else Fore.RED
    return f"{color}{value:+.{decimals}f}%{Style.RESET_ALL}"


def format_pnl(pnl: float, currency: str = "USD") -> str:
    """
    Format PnL with color coding.
    
    Args:
        pnl: PnL amount
        currency: Currency code
        
    Returns:
        Formatted PnL string with color
    """
    color = Fore.GREEN if pnl >= 0 else Fore.RED
    formatted_amount = format_currency(pnl, currency)
    return f"{color}{formatted_amount}{Style.RESET_ALL}"


def create_progress_bar(current: int, total: int, width: int = 50) -> str:
    """
    Create a text-based progress bar.
    
    Args:
        current: Current progress
        total: Total items
        width: Width of progress bar
        
    Returns:
        Progress bar string
    """
    if total == 0:
        return "[" + "=" * width + "]"
    
    progress = current / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = progress * 100
    
    return f"[{bar}] {percentage:.1f}% ({current}/{total})"


def ensure_directory(path: str) -> Path:
    """
    Ensure directory exists, create if it doesn't.
    
    Args:
        path: Directory path
        
    Returns:
        Path object
    """
    dir_path = Path(path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero.
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value if division by zero
        
    Returns:
        Division result or default
    """
    return numerator / denominator if denominator != 0 else default


def round_to_tick_size(price: float, tick_size: float) -> float:
    """
    Round price to nearest tick size.
    
    Args:
        price: Price to round
        tick_size: Minimum tick size
        
    Returns:
        Rounded price
    """
    if tick_size <= 0:
        return price
    
    return round(price / tick_size) * tick_size


def calculate_compound_return(initial_value: float, final_value: float, 
                            periods: int) -> float:
    """
    Calculate compound annual growth rate.
    
    Args:
        initial_value: Starting value
        final_value: Ending value
        periods: Number of periods (years)
        
    Returns:
        Compound annual growth rate
    """
    if initial_value <= 0 or periods <= 0:
        return 0.0
    
    return ((final_value / initial_value) ** (1 / periods) - 1) * 100


def timestamp_to_string(timestamp: datetime, format: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Convert timestamp to formatted string.
    
    Args:
        timestamp: Datetime object
        format: Format string
        
    Returns:
        Formatted timestamp string
    """
    return timestamp.strftime(format)


def print_banner(text: str, width: int = 60, char: str = "=") -> None:
    """
    Print a banner with text.
    
    Args:
        text: Text to display
        width: Banner width
        char: Character to use for banner
    """
    print(char * width)
    print(f"{text:^{width}}")
    print(char * width)


def print_summary_table(data: Dict[str, Any], title: str = "Summary") -> None:
    """
    Print a formatted summary table.
    
    Args:
        data: Dictionary of key-value pairs
        title: Table title
    """
    print_banner(title)
    
    # Calculate column widths
    key_width = max(len(str(k)) for k in data.keys()) + 2
    value_width = max(len(str(v)) for v in data.values()) + 2
    
    # Print table
    for key, value in data.items():
        if isinstance(value, float):
            if abs(value) >= 1000:
                value_str = f"{value:,.2f}"
            else:
                value_str = f"{value:.4f}"
        else:
            value_str = str(value)
        
        print(f"{key:<{key_width}} : {value_str}")
    
    print("=" * (key_width + value_width + 3))
