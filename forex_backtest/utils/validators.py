"""
Input validation utilities for Forex backtesting system.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, date
import re

from ..config.trading_params import SYMBOL_INFO, SUPPORTED_TIMEFRAMES, DATA_SOURCES


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


def validate_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate configuration dictionary.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        List of validation errors (empty if valid)
    """
    errors = []
    
    # Validate trading configuration
    trading_config = config.get('trading', {})
    errors.extend(_validate_trading_config(trading_config))
    
    # Validate strategy configuration
    strategy_config = config.get('strategy', {})
    errors.extend(_validate_strategy_config(strategy_config))
    
    # Validate risk management configuration
    risk_config = config.get('risk_management', {})
    errors.extend(_validate_risk_config(risk_config))
    
    # Validate data configuration
    data_config = config.get('data', {})
    errors.extend(_validate_data_config(data_config))
    
    return errors


def _validate_trading_config(config: Dict[str, Any]) -> List[str]:
    """Validate trading configuration section."""
    errors = []
    
    # Symbol validation
    symbol = config.get('symbol')
    if not symbol:
        errors.append("Trading symbol is required")
    elif not validate_symbol(symbol):
        errors.append(f"Unsupported symbol: {symbol}")
    
    # Timeframe validation
    timeframe = config.get('timeframe')
    if not timeframe:
        errors.append("Trading timeframe is required")
    elif not validate_timeframe(timeframe):
        errors.append(f"Unsupported timeframe: {timeframe}")
    
    # Balance validation
    initial_balance = config.get('initial_balance')
    if not initial_balance:
        errors.append("Initial balance is required")
    elif not isinstance(initial_balance, (int, float)) or initial_balance <= 0:
        errors.append("Initial balance must be a positive number")
    elif initial_balance < 1000:
        errors.append("Initial balance should be at least $1000")
    
    # Risk per trade validation
    risk_per_trade = config.get('risk_per_trade')
    if risk_per_trade is not None:
        if not isinstance(risk_per_trade, (int, float)):
            errors.append("Risk per trade must be a number")
        elif risk_per_trade <= 0 or risk_per_trade > 0.1:
            errors.append("Risk per trade must be between 0 and 10%")
    
    return errors


def _validate_strategy_config(config: Dict[str, Any]) -> List[str]:
    """Validate strategy configuration section."""
    errors = []
    
    # EMA periods validation
    ema_fast = config.get('ema_fast')
    ema_slow = config.get('ema_slow')
    
    if ema_fast is not None:
        if not isinstance(ema_fast, int) or ema_fast <= 0:
            errors.append("Fast EMA period must be a positive integer")
        elif ema_fast < 5 or ema_fast > 200:
            errors.append("Fast EMA period should be between 5 and 200")
    
    if ema_slow is not None:
        if not isinstance(ema_slow, int) or ema_slow <= 0:
            errors.append("Slow EMA period must be a positive integer")
        elif ema_slow < 10 or ema_slow > 500:
            errors.append("Slow EMA period should be between 10 and 500")
    
    # Cross-validation
    if ema_fast and ema_slow and ema_fast >= ema_slow:
        errors.append("Fast EMA period must be less than slow EMA period")
    
    return errors


def _validate_risk_config(config: Dict[str, Any]) -> List[str]:
    """Validate risk management configuration section."""
    errors = []
    
    # Martingale orders validation
    max_martingale = config.get('max_martingale_orders')
    if max_martingale is not None:
        if not isinstance(max_martingale, int) or max_martingale < 0:
            errors.append("Max martingale orders must be a non-negative integer")
        elif max_martingale > 10:
            errors.append("Max martingale orders should not exceed 10")
    
    # ATR period validation
    atr_period = config.get('atr_period')
    if atr_period is not None:
        if not isinstance(atr_period, int) or atr_period <= 0:
            errors.append("ATR period must be a positive integer")
        elif atr_period < 5 or atr_period > 50:
            errors.append("ATR period should be between 5 and 50")
    
    # ATR multipliers validation
    atr_mult_target = config.get('atr_multiplier_target')
    if atr_mult_target is not None:
        if not isinstance(atr_mult_target, (int, float)) or atr_mult_target <= 0:
            errors.append("ATR multiplier target must be a positive number")
        elif atr_mult_target > 10:
            errors.append("ATR multiplier target seems too high (>10)")
    
    atr_mult_martingale = config.get('atr_multiplier_martingale')
    if atr_mult_martingale is not None:
        if not isinstance(atr_mult_martingale, (int, float)) or atr_mult_martingale <= 0:
            errors.append("ATR multiplier martingale must be a positive number")
        elif atr_mult_martingale > 5:
            errors.append("ATR multiplier martingale seems too high (>5)")
    
    return errors


def _validate_data_config(config: Dict[str, Any]) -> List[str]:
    """Validate data configuration section."""
    errors = []

    # Data source validation
    source = config.get('source')
    if source and source not in DATA_SOURCES:
        errors.append(f"Unsupported data source: {source}")

    # Local data specific validation
    if source == 'local':
        local_data_path = config.get('local_data_path')
        if local_data_path:
            from pathlib import Path
            path = Path(local_data_path)
            if not path.exists():
                errors.append(f"Local data path does not exist: {local_data_path}")
            elif not path.is_dir():
                errors.append(f"Local data path is not a directory: {local_data_path}")

        file_pattern = config.get('local_file_pattern')
        if file_pattern:
            if '{symbol}' not in file_pattern:
                errors.append("Local file pattern must contain {symbol} placeholder")
            if '{timeframe}' not in file_pattern:
                errors.append("Local file pattern must contain {timeframe} placeholder")

    # Date validation
    start_date = config.get('start_date')
    end_date = config.get('end_date')

    if start_date:
        if not _validate_date_string(start_date):
            errors.append("Start date must be in YYYY-MM-DD format")

    if end_date:
        if not _validate_date_string(end_date):
            errors.append("End date must be in YYYY-MM-DD format")

    if start_date and end_date:
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')

            if start >= end:
                errors.append("Start date must be before end date")

            # Check if date range is reasonable
            days_diff = (end - start).days
            if days_diff < 30:
                errors.append("Date range should be at least 30 days for meaningful backtesting")
            elif days_diff > 3650:  # 10 years
                errors.append("Date range exceeds 10 years, consider shorter period")

        except ValueError:
            pass  # Date format errors already caught above

    return errors


def validate_symbol(symbol: str) -> bool:
    """
    Validate trading symbol.
    
    Args:
        symbol: Trading symbol
        
    Returns:
        True if valid symbol
    """
    return symbol.upper() in SYMBOL_INFO


def validate_timeframe(timeframe: str) -> bool:
    """
    Validate timeframe.
    
    Args:
        timeframe: Timeframe string
        
    Returns:
        True if valid timeframe
    """
    return timeframe in SUPPORTED_TIMEFRAMES


def validate_data(data: pd.DataFrame, symbol: str) -> List[str]:
    """
    Validate market data DataFrame.
    
    Args:
        data: Market data DataFrame
        symbol: Trading symbol
        
    Returns:
        List of validation errors
    """
    errors = []
    
    if data.empty:
        errors.append("Data is empty")
        return errors
    
    # Check required columns
    required_columns = ['Open', 'High', 'Low', 'Close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        errors.append(f"Missing required columns: {missing_columns}")
    
    # Check data types
    for col in required_columns:
        if col in data.columns and not pd.api.types.is_numeric_dtype(data[col]):
            errors.append(f"Column '{col}' must be numeric")
    
    # Check for missing values
    if data.isnull().any().any():
        null_counts = data.isnull().sum()
        null_columns = null_counts[null_counts > 0].to_dict()
        errors.append(f"Missing values found: {null_columns}")
    
    # Check OHLC relationships
    if all(col in data.columns for col in required_columns):
        invalid_ohlc = (
            (data['High'] < data['Low']) |
            (data['High'] < data['Open']) |
            (data['High'] < data['Close']) |
            (data['Low'] > data['Open']) |
            (data['Low'] > data['Close'])
        )
        
        if invalid_ohlc.any():
            errors.append(f"Invalid OHLC relationships found in {invalid_ohlc.sum()} rows")
    
    # Check for duplicate timestamps
    if isinstance(data.index, pd.DatetimeIndex):
        if data.index.duplicated().any():
            errors.append("Duplicate timestamps found in data")
    
    # Check data range
    if len(data) < 100:
        errors.append("Insufficient data points (minimum 100 recommended)")
    
    # Check for extreme values
    for col in ['Open', 'High', 'Low', 'Close']:
        if col in data.columns:
            col_data = data[col]
            if (col_data <= 0).any():
                errors.append(f"Non-positive values found in {col}")
            
            # Check for extreme price movements (>50% in one bar)
            if len(col_data) > 1:
                price_changes = col_data.pct_change().abs()
                extreme_changes = price_changes > 0.5
                if extreme_changes.any():
                    errors.append(f"Extreme price movements (>50%) found in {col}")
    
    return errors


def _validate_date_string(date_string: str) -> bool:
    """Validate date string format (YYYY-MM-DD)."""
    pattern = r'^\d{4}-\d{2}-\d{2}$'
    if not re.match(pattern, date_string):
        return False
    
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def validate_order_parameters(symbol: str, side: str, size: float, 
                            order_type: str, price: Optional[float] = None) -> List[str]:
    """
    Validate order parameters.
    
    Args:
        symbol: Trading symbol
        side: Order side ('buy' or 'sell')
        size: Order size
        order_type: Order type ('market', 'limit', 'stop')
        price: Order price (for limit/stop orders)
        
    Returns:
        List of validation errors
    """
    errors = []
    
    # Symbol validation
    if not validate_symbol(symbol):
        errors.append(f"Invalid symbol: {symbol}")
    
    # Side validation
    if side.lower() not in ['buy', 'sell']:
        errors.append(f"Invalid order side: {side}")
    
    # Size validation
    if not isinstance(size, (int, float)) or size <= 0:
        errors.append("Order size must be a positive number")
    elif size > 100:  # Arbitrary large size limit
        errors.append("Order size seems too large (>100 lots)")
    elif size < 0.01:  # Minimum size
        errors.append("Order size too small (minimum 0.01 lots)")
    
    # Order type validation
    if order_type.lower() not in ['market', 'limit', 'stop']:
        errors.append(f"Invalid order type: {order_type}")
    
    # Price validation for limit/stop orders
    if order_type.lower() in ['limit', 'stop']:
        if price is None:
            errors.append(f"{order_type} orders require a price")
        elif not isinstance(price, (int, float)) or price <= 0:
            errors.append("Order price must be a positive number")
    
    return errors


def validate_backtest_parameters(start_date: str, end_date: str, 
                                initial_balance: float, symbol: str) -> List[str]:
    """
    Validate backtest parameters.
    
    Args:
        start_date: Start date string
        end_date: End date string
        initial_balance: Initial account balance
        symbol: Trading symbol
        
    Returns:
        List of validation errors
    """
    errors = []
    
    # Date validation
    if not _validate_date_string(start_date):
        errors.append("Invalid start date format (use YYYY-MM-DD)")
    
    if not _validate_date_string(end_date):
        errors.append("Invalid end date format (use YYYY-MM-DD)")
    
    if _validate_date_string(start_date) and _validate_date_string(end_date):
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        if start >= end:
            errors.append("Start date must be before end date")
        
        # Check if dates are not too far in the future
        if end > datetime.now():
            errors.append("End date cannot be in the future")
    
    # Balance validation
    if not isinstance(initial_balance, (int, float)) or initial_balance <= 0:
        errors.append("Initial balance must be a positive number")
    elif initial_balance < 100:
        errors.append("Initial balance too small (minimum $100)")
    
    # Symbol validation
    if not validate_symbol(symbol):
        errors.append(f"Unsupported symbol: {symbol}")
    
    return errors


def validate_local_csv_file(file_path: str) -> List[str]:
    """
    Validate a local CSV file for forex data.

    Args:
        file_path: Path to CSV file

    Returns:
        List of validation errors
    """
    errors = []

    try:
        from pathlib import Path
        path = Path(file_path)

        if not path.exists():
            errors.append(f"File does not exist: {file_path}")
            return errors

        if not path.is_file():
            errors.append(f"Path is not a file: {file_path}")
            return errors

        # Try to read the CSV file
        try:
            data = pd.read_csv(file_path, nrows=10)  # Read only first 10 rows for validation
        except Exception as e:
            errors.append(f"Cannot read CSV file: {e}")
            return errors

        if data.empty:
            errors.append("CSV file is empty")
            return errors

        # Check for datetime column
        datetime_columns = ['Date', 'Datetime', 'Time', 'Timestamp', 'date', 'datetime', 'time']
        has_datetime = any(col in data.columns for col in datetime_columns)

        if not has_datetime and len(data.columns) > 0:
            # Check if first column looks like datetime
            try:
                pd.to_datetime(data.iloc[0, 0])
                has_datetime = True
            except Exception:
                pass

        if not has_datetime:
            errors.append("No datetime column found in CSV file")

        # Check for OHLC columns (case-insensitive)
        required_cols = ['Open', 'High', 'Low', 'Close']
        available_cols = [col.lower() for col in data.columns]

        missing_ohlc = []
        for col in required_cols:
            if col.lower() not in available_cols:
                missing_ohlc.append(col)

        if missing_ohlc:
            errors.append(f"Missing OHLC columns: {missing_ohlc}")

        # Check for numeric data in OHLC columns
        for col in data.columns:
            if col.lower() in ['open', 'high', 'low', 'close']:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    errors.append(f"Column '{col}' should contain numeric data")

    except Exception as e:
        errors.append(f"Error validating CSV file: {e}")

    return errors


def validate_local_data_directory(directory_path: str, symbol: str = None,
                                 timeframe: str = None) -> Dict[str, Any]:
    """
    Validate local data directory and optionally check for specific files.

    Args:
        directory_path: Path to local data directory
        symbol: Optional symbol to check for
        timeframe: Optional timeframe to check for

    Returns:
        Dictionary with validation results
    """
    from pathlib import Path

    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'files_found': 0,
        'valid_files': 0,
        'files': []
    }

    try:
        path = Path(directory_path)

        if not path.exists():
            result['valid'] = False
            result['errors'].append(f"Directory does not exist: {directory_path}")
            return result

        if not path.is_dir():
            result['valid'] = False
            result['errors'].append(f"Path is not a directory: {directory_path}")
            return result

        # Find CSV files
        csv_files = list(path.glob("*.csv"))
        result['files_found'] = len(csv_files)

        if len(csv_files) == 0:
            result['warnings'].append("No CSV files found in directory")

        # Validate each CSV file
        for csv_file in csv_files:
            file_errors = validate_local_csv_file(str(csv_file))

            file_info = {
                'filename': csv_file.name,
                'path': str(csv_file),
                'valid': len(file_errors) == 0,
                'errors': file_errors
            }

            if file_info['valid']:
                result['valid_files'] += 1

            result['files'].append(file_info)

        # Check for specific symbol/timeframe if requested
        if symbol and timeframe:
            patterns = [
                f"{symbol}_{timeframe}.csv",
                f"{symbol}{timeframe}.csv",
                f"{symbol}_{timeframe}_*.csv"
            ]

            found_specific = False
            for pattern in patterns:
                if list(path.glob(pattern)):
                    found_specific = True
                    break

            if not found_specific:
                result['warnings'].append(f"No file found for {symbol} {timeframe}")

    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"Error validating directory: {e}")

    return result
