"""
EMA Crossover Strategy implementation for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_strategy import BaseStrategy, OrderSide, Position
from .position_manager import PositionManager
from ..indicators.signals import SignalGenerator, SignalType
from ..indicators.ema import calculate_ema
from ..indicators.volatility import calculate_atr
from ..config.trading_params import SYMBOL_INFO

logger = logging.getLogger(__name__)


class EMACrossStrategy(BaseStrategy):
    """
    EMA Crossover Strategy with martingale position management.
    
    Strategy Rules:
    - Entry: EMA 55 crosses above/below EMA 89
    - Exit: ATR-based profit targets (no stop loss)
    - Position Management: Martingale system with ATR-based spacing
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize EMA crossover strategy.
        
        Args:
            config: Strategy configuration
        """
        super().__init__(config)
        
        # Strategy parameters
        self.strategy_config = config.get('strategy', {})
        self.trading_config = config.get('trading', {})
        self.risk_config = config.get('risk_management', {})
        
        self.ema_fast = self.strategy_config.get('ema_fast', 55)
        self.ema_slow = self.strategy_config.get('ema_slow', 89)
        self.symbol = self.trading_config.get('symbol', 'AUDUSD')
        self.initial_balance = self.trading_config.get('initial_balance', 10000)
        self.risk_per_trade = self.trading_config.get('risk_per_trade', 0.02)
        self.min_profit_pips = self.trading_config.get('min_profit_pips', 5)
        
        # ATR parameters
        self.atr_period = self.risk_config.get('atr_period', 14)
        self.atr_multiplier_target = self.risk_config.get('atr_multiplier_target', 2.0)
        
        # Initialize components
        self.signal_generator = SignalGenerator(config)
        self.position_manager = PositionManager(config)
        
        # Strategy state
        self.current_balance = self.initial_balance
        self.signals_df = None
        self.current_atr = None
        
        # Get symbol info
        self.symbol_info = SYMBOL_INFO.get(self.symbol, {})
        self.pip_value = self.symbol_info.get('pip_value', 0.0001)
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals from market data."""
        logger.info(f"Generating signals for {len(data)} bars of {self.symbol} data")
        
        # Generate all signals using signal generator
        self.signals_df = self.signal_generator.generate_all_signals(data)
        
        # Log signal counts
        signal_counts = (self.signals_df['Trading_Signal'] != 0).sum()
        logger.info(f"Generated {signal_counts} raw trading signals")
        
        # Add additional strategy-specific indicators
        self.signals_df['Strategy_Signal'] = self._generate_strategy_signals(data)
        
        # Log final signal counts
        strategy_signal_counts = (self.signals_df['Strategy_Signal'] != 0).sum()
        logger.info(f"Final strategy signals after filtering: {strategy_signal_counts}")
        
        return self.signals_df
    
    def _generate_strategy_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        Generate strategy-specific signals.
        
        Args:
            data: OHLC DataFrame
            
        Returns:
            Strategy signals series
        """
        # Use the confirmed signals from signal generator
        base_signals = self.signals_df['Trading_Signal']
        strategy_signals = pd.Series(0, index=data.index)
        
        # Apply additional filters specific to this strategy
        for i in range(len(data)):
            if base_signals.iloc[i] != 0:
                # Additional validation can be added here
                # For now, we use the base signals directly
                strategy_signals.iloc[i] = base_signals.iloc[i]
        
        return strategy_signals
    
    def should_enter_trade(self, bar_data: pd.Series, signals: pd.Series) -> Optional[OrderSide]:
        """Determine if a trade should be entered."""
        # Check if we already have open positions (limit to one position at a time)
        if len(self.position_manager.get_open_positions()) > 0:
            return None
        
        # Get current signal
        current_signal = signals.get('Strategy_Signal', 0)
        
        # Log signal value for debugging
        if current_signal != 0:
            logger.debug(f"Signal detected: {current_signal} at {bar_data.name}")
        
        if current_signal == 0:
            return None
        
        # Determine entry side
        if current_signal > 0:
            return OrderSide.BUY
        elif current_signal < 0:
            return OrderSide.SELL
        
        return None
    
    def should_exit_trade(self, position: Position, bar_data: pd.Series) -> bool:
        """
        Determine if a position should be exited.
        
        Args:
            position: Current position
            bar_data: Current bar data
            
        Returns:
            True if position should be exited
        """
        current_price = bar_data['Close']
        
        # Get current ATR
        if self.current_atr is None or pd.isna(self.current_atr):
            return False
        
        # Use martingale manager to determine if position should be closed
        return self.position_manager.martingale_manager.should_close_at_profit(
            position, current_price, self.current_atr, self.min_profit_pips
        )
    
    def calculate_position_size(self, signal_strength: float, account_balance: float) -> float:
        """
        Calculate position size for a trade.
        
        Args:
            signal_strength: Signal strength (not used in this implementation)
            account_balance: Current account balance
            
        Returns:
            Position size in lots
        """
        # Calculate risk amount
        risk_amount = account_balance * self.risk_per_trade
        
        # Use ATR for position sizing if available
        if self.current_atr and self.current_atr > 0:
            # Convert ATR to pips
            atr_pips = self.current_atr / self.pip_value
            
            # Calculate position size based on ATR risk
            if atr_pips > 0:
                # Position size = Risk Amount / (ATR in pips * pip value)
                position_size = risk_amount / (atr_pips * self.pip_value * 10000)  # 10000 for standard lot
                
                # Ensure minimum position size
                position_size = max(position_size, 0.01)  # Minimum 0.01 lots
                
                # Round to 2 decimal places
                return round(position_size, 2)
        
        # Fallback: fixed position size
        return 0.1  # 0.1 lots
    
    def on_bar(self, bar_data: pd.Series, bar_index: int, signals: pd.Series) -> List:
        """Process a new bar of data."""
        self.current_bar_index = bar_index
        self.current_timestamp = bar_data.name
        self.current_data = bar_data
        
        # Log every 1000 bars to show progress
        if bar_index % 1000 == 0:
            logger.info(f"Processing bar {bar_index} at {self.current_timestamp}")
        
        # Update current ATR
        if 'ATR' in signals:
            self.current_atr = signals['ATR']
        
        orders = []
        current_price = bar_data['Close']
        
        # Update positions and get any martingale orders
        if self.current_atr:
            position_orders = self.position_manager.update_positions(
                current_price, self.current_atr, self.current_timestamp
            )
            orders.extend(position_orders)
        
        # Check for new entry signals
        entry_side = self.should_enter_trade(bar_data, signals)
        
        if entry_side:
            logger.info(f"Entry signal detected at bar {bar_index}: {entry_side.value}")
            # Calculate position size
            position_size = self.calculate_position_size(1.0, self.current_balance)
            
            # Create entry order
            from .base_strategy import Order, OrderType
            
            order = Order(
                order_id=f"entry_{self.current_timestamp}_{len(self.orders)}",
                symbol=self.symbol,
                side=entry_side,
                size=position_size,
                order_type=OrderType.MARKET,
                timestamp=self.current_timestamp
            )
            
            orders.append(order)
            self.orders[order.order_id] = order
            
            logger.info(f"Generated entry signal: {entry_side.value} {position_size} {self.symbol}")
        
        return orders
    
    def fill_order(self, order_id: str, fill_price: float, fill_timestamp: datetime) -> None:
        """
        Handle order fill.
        
        Args:
            order_id: Order ID that was filled
            fill_price: Fill price
            fill_timestamp: Fill timestamp
        """
        if order_id in self.orders:
            order = self.orders[order_id]
            order.fill(fill_price, fill_timestamp)
            
            # Create or update position
            position = self.position_manager.fill_order(order_id, fill_price, fill_timestamp)
            
            if position:
                logger.info(f"Order {order_id} filled: {order.side.value} {order.size} "
                           f"{order.symbol} at {fill_price}")
    
    def close_position(self, position_id: str, exit_price: float, 
                      exit_timestamp: datetime, reason: str = "manual") -> Optional[float]:
        """
        Close a position.
        
        Args:
            position_id: Position ID to close
            exit_price: Exit price
            exit_timestamp: Exit timestamp
            reason: Reason for closing
            
        Returns:
            Realized PnL
        """
        pnl = self.position_manager.close_position(position_id, exit_price, exit_timestamp, reason)
        
        if pnl is not None:
            # Update balance
            self.current_balance += pnl
            
            logger.info(f"Position {position_id} closed: PnL = {pnl:.2f}, "
                       f"New balance = {self.current_balance:.2f}")
        
        return pnl
    
    def get_open_positions(self) -> Dict[str, Position]:
        """Get all open positions."""
        return self.position_manager.get_open_positions()
    
    def get_trade_history(self) -> List[Dict[str, Any]]:
        """Get trade history."""
        return self.position_manager.trade_history.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        # Get basic trade statistics
        trade_stats = self.position_manager.get_trade_statistics()
        
        # Add strategy-specific metrics
        position_summary = self.position_manager.get_position_summary()
        
        # Calculate additional metrics
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance * 100
        
        return {
            **trade_stats,
            'initial_balance': self.initial_balance,
            'current_balance': self.current_balance,
            'total_return_percent': total_return,
            'current_positions': position_summary['total_positions'],
            'unrealized_pnl': position_summary['total_unrealized_pnl'],
            'strategy_name': self.name,
            'symbol': self.symbol,
            'ema_fast': self.ema_fast,
            'ema_slow': self.ema_slow
        }
    
    def get_signals_dataframe(self) -> Optional[pd.DataFrame]:
        """Get the signals DataFrame."""
        return self.signals_df
    
    def reset(self) -> None:
        """Reset strategy state."""
        self.current_balance = self.initial_balance
        self.position_manager = PositionManager(self.config)
        self.orders = {}
        self.trade_history = []
        self.signals_df = None
        self.current_atr = None
