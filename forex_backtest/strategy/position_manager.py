"""
Position and order management with martingale system support.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from uuid import uuid4

from .base_strategy import Order, Position, OrderSide, OrderType, OrderStatus
from ..indicators.volatility import calculate_atr

logger = logging.getLogger(__name__)


class MartingaleManager:
    """
    Martingale position management system.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize martingale manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.risk_config = config.get('risk_management', {})
        
        self.max_martingale_orders = self.risk_config.get('max_martingale_orders', 5)
        self.atr_multiplier_martingale = self.risk_config.get('atr_multiplier_martingale', 1.5)
        self.position_size_multiplier = self.risk_config.get('position_size_multiplier', 1.5)
        self.atr_multiplier_target = self.risk_config.get('atr_multiplier_target', 2.0)
        
    def should_add_martingale(self, position: Position, current_price: float, 
                             atr_value: float) -> bool:
        """
        Determine if a martingale order should be added.
        
        Args:
            position: Current position
            current_price: Current market price
            atr_value: Current ATR value
            
        Returns:
            True if martingale order should be added
        """
        if len(position.martingale_orders) >= self.max_martingale_orders:
            return False
        
        # Calculate distance from average entry price
        price_distance = abs(current_price - position.average_price)
        martingale_trigger_distance = atr_value * self.atr_multiplier_martingale
        
        # Check if price has moved against position by trigger distance
        if position.side == OrderSide.BUY:
            # For long positions, add martingale when price drops
            return current_price < (position.average_price - martingale_trigger_distance)
        else:
            # For short positions, add martingale when price rises
            return current_price > (position.average_price + martingale_trigger_distance)
    
    def calculate_martingale_size(self, position: Position, martingale_level: int) -> float:
        """
        Calculate size for martingale order.
        
        Args:
            position: Current position
            martingale_level: Martingale level (0-based)
            
        Returns:
            Order size for martingale
        """
        base_size = position.size
        multiplier = self.position_size_multiplier ** (martingale_level + 1)
        return base_size * multiplier
    
    def calculate_profit_target(self, position: Position, atr_value: float) -> float:
        """
        Calculate profit target price for position.
        
        Args:
            position: Current position
            atr_value: Current ATR value
            
        Returns:
            Profit target price
        """
        target_distance = atr_value * self.atr_multiplier_target
        
        if position.side == OrderSide.BUY:
            return position.average_price + target_distance
        else:
            return position.average_price - target_distance
    
    def should_close_at_profit(self, position: Position, current_price: float,
                              atr_value: float, min_profit_pips: float = 5) -> bool:
        """
        Determine if position should be closed at profit.
        
        Args:
            position: Current position
            current_price: Current market price
            atr_value: Current ATR value
            min_profit_pips: Minimum profit in pips
            
        Returns:
            True if position should be closed
        """
        # Calculate current profit in pips (assuming 4-digit quotes)
        if position.side == OrderSide.BUY:
            profit_pips = (current_price - position.average_price) * 10000
        else:
            profit_pips = (position.average_price - current_price) * 10000
        
        # Check minimum profit requirement
        if profit_pips < min_profit_pips:
            return False
        
        # Check ATR-based target
        profit_target = self.calculate_profit_target(position, atr_value)
        
        if position.side == OrderSide.BUY:
            return current_price >= profit_target
        else:
            return current_price <= profit_target


class PositionManager:
    """
    Main position manager handling all position operations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize position manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.positions = {}  # position_id -> Position
        self.orders = {}     # order_id -> Order
        self.trade_history = []
        
        # Initialize martingale manager
        self.martingale_manager = MartingaleManager(config)
        
        # Configuration
        self.trading_config = config.get('trading', {})
        self.symbol = self.trading_config.get('symbol', 'AUDUSD')
        self.min_profit_pips = self.trading_config.get('min_profit_pips', 5)
        
    def create_position(self, order: Order, fill_price: float, 
                       fill_timestamp: datetime) -> Position:
        """
        Create a new position from filled order.
        
        Args:
            order: Filled order
            fill_price: Fill price
            fill_timestamp: Fill timestamp
            
        Returns:
            Created position
        """
        position_id = str(uuid4())
        
        position = Position(
            position_id=position_id,
            symbol=order.symbol,
            side=order.side,
            size=order.size,
            entry_price=fill_price,
            entry_timestamp=fill_timestamp
        )
        
        self.positions[position_id] = position
        
        logger.info(f"Created position {position_id}: {order.side.value} {order.size} "
                   f"{order.symbol} at {fill_price}")
        
        return position
    
    def close_position(self, position_id: str, exit_price: float, 
                      exit_timestamp: datetime, reason: str = "manual") -> Optional[float]:
        """
        Close a position.
        
        Args:
            position_id: Position ID to close
            exit_price: Exit price
            exit_timestamp: Exit timestamp
            reason: Reason for closing
            
        Returns:
            Realized PnL or None if position not found
        """
        if position_id not in self.positions:
            logger.warning(f"Position {position_id} not found")
            return None
        
        position = self.positions[position_id]
        realized_pnl = position.close_full(exit_price)
        
        # Record trade in history
        trade_record = {
            'position_id': position_id,
            'symbol': position.symbol,
            'side': position.side.value,
            'entry_size': position.size,
            'total_size': position.total_size,
            'entry_price': position.entry_price,
            'average_price': position.average_price,
            'exit_price': exit_price,
            'entry_timestamp': position.entry_timestamp,
            'exit_timestamp': exit_timestamp,
            'pnl': realized_pnl,
            'martingale_orders': len(position.martingale_orders),
            'close_reason': reason
        }
        
        self.trade_history.append(trade_record)
        
        # Remove position
        del self.positions[position_id]
        
        logger.info(f"Closed position {position_id}: PnL = {realized_pnl:.2f} ({reason})")
        
        return realized_pnl
    
    def update_positions(self, current_price: float, atr_value: float, 
                        timestamp: datetime) -> List[Order]:
        """
        Update all positions and generate orders if needed.
        
        Args:
            current_price: Current market price
            atr_value: Current ATR value
            timestamp: Current timestamp
            
        Returns:
            List of orders to be placed
        """
        orders = []
        positions_to_close = []
        
        for position_id, position in self.positions.items():
            # Update position with current price
            position.update_price(current_price)
            
            # Check if position should be closed at profit
            if self.martingale_manager.should_close_at_profit(
                position, current_price, atr_value, self.min_profit_pips
            ):
                positions_to_close.append((position_id, "profit_target"))
                continue
            
            # Check if martingale order should be added
            if self.martingale_manager.should_add_martingale(
                position, current_price, atr_value
            ):
                martingale_order = self._create_martingale_order(
                    position, current_price, timestamp
                )
                if martingale_order:
                    orders.append(martingale_order)
        
        # Close positions that hit profit targets
        for position_id, reason in positions_to_close:
            self.close_position(position_id, current_price, timestamp, reason)
        
        return orders
    
    def _create_martingale_order(self, position: Position, current_price: float,
                                timestamp: datetime) -> Optional[Order]:
        """
        Create a martingale order for a position.
        
        Args:
            position: Position to add martingale order to
            current_price: Current market price
            timestamp: Current timestamp
            
        Returns:
            Martingale order or None
        """
        martingale_level = len(position.martingale_orders)
        
        if martingale_level >= self.martingale_manager.max_martingale_orders:
            return None
        
        # Calculate martingale order size
        martingale_size = self.martingale_manager.calculate_martingale_size(
            position, martingale_level
        )
        
        # Create martingale order
        order = Order(
            order_id=f"martingale_{position.position_id}_{martingale_level}",
            symbol=position.symbol,
            side=position.side,  # Same side as original position
            size=martingale_size,
            order_type=OrderType.MARKET,
            timestamp=timestamp
        )
        
        order.martingale_level = martingale_level + 1
        
        # Add to position's martingale orders
        position.add_martingale_order(order)
        
        # Add to orders dictionary
        self.orders[order.order_id] = order
        
        logger.info(f"Created martingale order level {martingale_level + 1} "
                   f"for position {position.position_id}: size {martingale_size}")
        
        return order
    
    def fill_order(self, order_id: str, fill_price: float, 
                  fill_timestamp: datetime) -> Optional[Position]:
        """
        Fill an order and update positions.
        
        Args:
            order_id: Order ID to fill
            fill_price: Fill price
            fill_timestamp: Fill timestamp
            
        Returns:
            Position (new or updated) or None
        """
        if order_id not in self.orders:
            logger.warning(f"Order {order_id} not found")
            return None
        
        order = self.orders[order_id]
        order.fill(fill_price, fill_timestamp)
        
        # Check if this is a new position or martingale order
        if order.martingale_level == 0:
            # New position
            return self.create_position(order, fill_price, fill_timestamp)
        else:
            # Martingale order - position should already exist
            # The order was already added to position in _create_martingale_order
            logger.info(f"Filled martingale order {order_id} at {fill_price}")
            return None
    
    def get_open_positions(self) -> Dict[str, Position]:
        """Get all open positions."""
        return self.positions.copy()
    
    def get_position_summary(self) -> Dict[str, Any]:
        """Get summary of all positions."""
        if not self.positions:
            return {
                'total_positions': 0,
                'total_unrealized_pnl': 0.0,
                'total_exposure': 0.0
            }
        
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_exposure = sum(pos.total_size * pos.current_price for pos in self.positions.values())
        
        return {
            'total_positions': len(self.positions),
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_exposure': total_exposure,
            'positions': [pos.to_dict() for pos in self.positions.values()]
        }
    
    def get_trade_statistics(self) -> Dict[str, Any]:
        """Get comprehensive trade statistics."""
        if not self.trade_history:
            return {'total_trades': 0}
        
        trades = self.trade_history
        pnls = [trade['pnl'] for trade in trades]
        
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]
        
        martingale_usage = [trade['martingale_orders'] for trade in trades]
        
        return {
            'total_trades': len(trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(winning_trades) / len(trades) if trades else 0,
            'total_pnl': sum(pnls),
            'average_win': np.mean(winning_trades) if winning_trades else 0,
            'average_loss': np.mean(losing_trades) if losing_trades else 0,
            'largest_win': max(pnls) if pnls else 0,
            'largest_loss': min(pnls) if pnls else 0,
            'average_martingale_orders': np.mean(martingale_usage) if martingale_usage else 0,
            'max_martingale_orders': max(martingale_usage) if martingale_usage else 0
        }
