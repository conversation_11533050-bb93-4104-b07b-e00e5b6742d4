"""
Base strategy class for Forex backtesting system.
"""

from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order types enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class Order:
    """
    Order class representing a trading order.
    """
    
    def __init__(self, order_id: str, symbol: str, side: OrderSide, 
                 size: float, order_type: OrderType, price: Optional[float] = None,
                 stop_loss: Optional[float] = None, take_profit: Optional[float] = None,
                 timestamp: Optional[datetime] = None):
        """
        Initialize order.
        
        Args:
            order_id: Unique order identifier
            symbol: Trading symbol
            side: Order side (BUY/SELL)
            size: Order size in lots
            order_type: Order type
            price: Order price (for limit/stop orders)
            stop_loss: Stop loss price
            take_profit: Take profit price
            timestamp: Order timestamp
        """
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.size = size
        self.order_type = order_type
        self.price = price
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.timestamp = timestamp or datetime.now()
        self.status = OrderStatus.PENDING
        self.fill_price = None
        self.fill_timestamp = None
        self.martingale_level = 0
        
    def fill(self, fill_price: float, fill_timestamp: datetime) -> None:
        """Mark order as filled."""
        self.status = OrderStatus.FILLED
        self.fill_price = fill_price
        self.fill_timestamp = fill_timestamp
        
    def cancel(self) -> None:
        """Cancel the order."""
        self.status = OrderStatus.CANCELLED
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary."""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'size': self.size,
            'order_type': self.order_type.value,
            'price': self.price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'timestamp': self.timestamp,
            'status': self.status.value,
            'fill_price': self.fill_price,
            'fill_timestamp': self.fill_timestamp,
            'martingale_level': self.martingale_level
        }


class Position:
    """
    Position class representing an open trading position.
    """
    
    def __init__(self, position_id: str, symbol: str, side: OrderSide,
                 size: float, entry_price: float, entry_timestamp: datetime):
        """
        Initialize position.
        
        Args:
            position_id: Unique position identifier
            symbol: Trading symbol
            side: Position side (BUY/SELL)
            size: Position size in lots
            entry_price: Entry price
            entry_timestamp: Entry timestamp
        """
        self.position_id = position_id
        self.symbol = symbol
        self.side = side
        self.size = size
        self.entry_price = entry_price
        self.entry_timestamp = entry_timestamp
        self.current_price = entry_price
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0
        self.martingale_orders = []
        self.total_size = size
        self.average_price = entry_price
        
    def update_price(self, current_price: float) -> None:
        """Update current price and unrealized PnL."""
        self.current_price = current_price
        
        if self.side == OrderSide.BUY:
            self.unrealized_pnl = (current_price - self.average_price) * self.total_size
        else:
            self.unrealized_pnl = (self.average_price - current_price) * self.total_size
    
    def add_martingale_order(self, order: Order) -> None:
        """Add a martingale order to the position."""
        self.martingale_orders.append(order)
        
        # Update average price and total size
        if order.status == OrderStatus.FILLED:
            total_value = self.average_price * self.total_size + order.fill_price * order.size
            self.total_size += order.size
            self.average_price = total_value / self.total_size
    
    def close_partial(self, size: float, exit_price: float) -> float:
        """
        Close part of the position.
        
        Args:
            size: Size to close
            exit_price: Exit price
            
        Returns:
            Realized PnL from partial close
        """
        if size > self.total_size:
            size = self.total_size
        
        if self.side == OrderSide.BUY:
            pnl = (exit_price - self.average_price) * size
        else:
            pnl = (self.average_price - exit_price) * size
        
        self.realized_pnl += pnl
        self.total_size -= size
        
        return pnl
    
    def close_full(self, exit_price: float) -> float:
        """
        Close the entire position.
        
        Args:
            exit_price: Exit price
            
        Returns:
            Total realized PnL
        """
        return self.close_partial(self.total_size, exit_price)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary."""
        return {
            'position_id': self.position_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'size': self.size,
            'total_size': self.total_size,
            'entry_price': self.entry_price,
            'average_price': self.average_price,
            'current_price': self.current_price,
            'entry_timestamp': self.entry_timestamp,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'martingale_orders': len(self.martingale_orders)
        }


class BaseStrategy(ABC):
    """
    Abstract base class for trading strategies.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize base strategy.
        
        Args:
            config: Strategy configuration
        """
        self.config = config
        self.name = self.__class__.__name__
        self.positions = {}  # position_id -> Position
        self.orders = {}     # order_id -> Order
        self.trade_history = []
        self.current_bar_index = 0
        self.current_timestamp = None
        self.current_data = None
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals from market data.
        
        Args:
            data: Market data DataFrame
            
        Returns:
            DataFrame with signals
        """
        pass
    
    @abstractmethod
    def should_enter_trade(self, bar_data: pd.Series, signals: pd.Series) -> Optional[OrderSide]:
        """
        Determine if a trade should be entered.
        
        Args:
            bar_data: Current bar data
            signals: Current signals
            
        Returns:
            OrderSide if trade should be entered, None otherwise
        """
        pass
    
    @abstractmethod
    def should_exit_trade(self, position: Position, bar_data: pd.Series) -> bool:
        """
        Determine if a position should be exited.
        
        Args:
            position: Current position
            bar_data: Current bar data
            
        Returns:
            True if position should be exited
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal_strength: float, account_balance: float) -> float:
        """
        Calculate position size for a trade.
        
        Args:
            signal_strength: Signal strength
            account_balance: Current account balance
            
        Returns:
            Position size in lots
        """
        pass
    
    def on_bar(self, bar_data: pd.Series, bar_index: int, signals: pd.Series) -> List[Order]:
        """
        Process a new bar of data.
        
        Args:
            bar_data: Current bar data
            bar_index: Bar index
            signals: Current signals
            
        Returns:
            List of orders to be placed
        """
        self.current_bar_index = bar_index
        self.current_timestamp = bar_data.name
        self.current_data = bar_data
        
        orders = []
        
        # Update existing positions
        self._update_positions(bar_data)
        
        # Check for exit signals
        self._check_exit_signals(bar_data, orders)
        
        # Check for entry signals
        self._check_entry_signals(bar_data, signals, orders)
        
        return orders
    
    def _update_positions(self, bar_data: pd.Series) -> None:
        """Update all open positions with current price."""
        current_price = bar_data['Close']
        
        for position in self.positions.values():
            position.update_price(current_price)
    
    def _check_exit_signals(self, bar_data: pd.Series, orders: List[Order]) -> None:
        """Check if any positions should be exited."""
        positions_to_close = []
        
        for position_id, position in self.positions.items():
            if self.should_exit_trade(position, bar_data):
                positions_to_close.append(position_id)
        
        # Close positions
        for position_id in positions_to_close:
            self._close_position(position_id, bar_data['Close'], orders)
    
    def _check_entry_signals(self, bar_data: pd.Series, signals: pd.Series, orders: List[Order]) -> None:
        """Check for new entry signals."""
        entry_side = self.should_enter_trade(bar_data, signals)
        
        if entry_side:
            # Calculate position size (placeholder - implement in derived class)
            position_size = self.calculate_position_size(1.0, 10000.0)
            
                # Create market order
            order = Order(
                order_id=f"order_{self.current_timestamp}_{len(self.orders)}",
                symbol=self.config.get('trading', {}).get('symbol', 'AUDUSD'),
                side=entry_side,
                size=position_size,
                order_type=OrderType.MARKET,
                timestamp=self.current_timestamp
            )

            orders.append(order)
            self.orders[order.order_id] = order
    
    def _close_position(self, position_id: str, exit_price: float, orders: List[Order]) -> None:
        """Close a position."""
        if position_id not in self.positions:
            return
        
        position = self.positions[position_id]
        
        # Create closing order
        closing_side = OrderSide.SELL if position.side == OrderSide.BUY else OrderSide.BUY
        
        order = Order(
            order_id=f"close_{position_id}_{self.current_timestamp}",
            symbol=position.symbol,
            side=closing_side,
            size=position.total_size,
            order_type=OrderType.MARKET,
            timestamp=self.current_timestamp
        )
        
        orders.append(order)
        self.orders[order.order_id] = order
        
        # Record trade
        realized_pnl = position.close_full(exit_price)
        
        trade_record = {
            'position_id': position_id,
            'symbol': position.symbol,
            'side': position.side.value,
            'size': position.total_size,
            'entry_price': position.average_price,
            'exit_price': exit_price,
            'entry_timestamp': position.entry_timestamp,
            'exit_timestamp': self.current_timestamp,
            'pnl': realized_pnl,
            'martingale_orders': len(position.martingale_orders)
        }
        
        self.trade_history.append(trade_record)
        
        # Remove position
        del self.positions[position_id]
        
        logger.info(f"Closed position {position_id}: PnL = {realized_pnl:.2f}")
    
    def get_open_positions(self) -> Dict[str, Position]:
        """Get all open positions."""
        return self.positions.copy()
    
    def get_trade_history(self) -> List[Dict[str, Any]]:
        """Get trade history."""
        return self.trade_history.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get basic performance summary."""
        if not self.trade_history:
            return {'total_trades': 0, 'total_pnl': 0.0}
        
        total_pnl = sum(trade['pnl'] for trade in self.trade_history)
        winning_trades = [trade for trade in self.trade_history if trade['pnl'] > 0]
        losing_trades = [trade for trade in self.trade_history if trade['pnl'] < 0]
        
        return {
            'total_trades': len(self.trade_history),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(winning_trades) / len(self.trade_history) if self.trade_history else 0,
            'total_pnl': total_pnl,
            'average_win': np.mean([trade['pnl'] for trade in winning_trades]) if winning_trades else 0,
            'average_loss': np.mean([trade['pnl'] for trade in losing_trades]) if losing_trades else 0,
        }
