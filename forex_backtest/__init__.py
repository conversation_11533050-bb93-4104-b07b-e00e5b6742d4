"""
Forex Backtesting System

A comprehensive, modular Python backtesting system for Forex trading
with EMA crossover strategies and martingale position management.

Author: AI Assistant
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# Import main components for easy access
from .config.settings import load_config
from .backtesting.backtest_engine import BacktestEngine
from .strategy.ema_cross_strategy import EMACrossStrategy
from .data.data_loader import DataLoader
from .visualization.charts import ChartGenerator
from .visualization.reports import ReportGenerator

__all__ = [
    "load_config",
    "BacktestEngine", 
    "EMACrossStrategy",
    "DataLoader",
    "ChartGenerator",
    "ReportGenerator"
]
