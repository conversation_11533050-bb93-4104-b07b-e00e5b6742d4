"""
Market data handling and preprocessing for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MarketData:
    """
    Market data container with preprocessing and validation capabilities.
    """
    
    def __init__(self, data: pd.DataFrame, symbol: str, timeframe: str):
        """
        Initialize MarketData instance.
        
        Args:
            data: OHLCV DataFrame with datetime index
            symbol: Trading symbol (e.g., 'AUDUSD')
            timeframe: Data timeframe (e.g., '5m')
        """
        self.symbol = symbol
        self.timeframe = timeframe
        self._data = self._validate_and_clean_data(data)
        self._indicators = {}
        
    @property
    def data(self) -> pd.DataFrame:
        """Get the market data DataFrame."""
        return self._data.copy()
    
    @property
    def ohlc(self) -> pd.DataFrame:
        """Get OHLC data only."""
        return self._data[['Open', 'High', 'Low', 'Close']].copy()
    
    @property
    def close(self) -> pd.Series:
        """Get close prices."""
        return self._data['Close'].copy()
    
    @property
    def volume(self) -> pd.Series:
        """Get volume data."""
        return self._data.get('Volume', pd.Series(index=self._data.index, dtype=float))
    
    def _validate_and_clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and clean market data.
        
        Args:
            data: Raw market data DataFrame
            
        Returns:
            Cleaned and validated DataFrame
            
        Raises:
            ValueError: If data is invalid
        """
        if data.empty:
            raise ValueError("Market data is empty")
        
        # Required columns
        required_columns = ['Open', 'High', 'Low', 'Close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Ensure datetime index
        if not isinstance(data.index, pd.DatetimeIndex):
            if 'Date' in data.columns:
                data = data.set_index('Date')
            elif 'Datetime' in data.columns:
                data = data.set_index('Datetime')
            else:
                raise ValueError("Data must have datetime index or Date/Datetime column")
        
        # Sort by datetime
        data = data.sort_index()
        
        # Remove duplicates
        data = data[~data.index.duplicated(keep='first')]
        
        # Validate OHLC relationships
        invalid_ohlc = (
            (data['High'] < data['Low']) |
            (data['High'] < data['Open']) |
            (data['High'] < data['Close']) |
            (data['Low'] > data['Open']) |
            (data['Low'] > data['Close'])
        )
        
        if invalid_ohlc.any():
            logger.warning(f"Found {invalid_ohlc.sum()} rows with invalid OHLC relationships")
            data = data[~invalid_ohlc]
        
        # Handle missing values
        if data.isnull().any().any():
            logger.warning("Found missing values in data, forward filling...")
            data = data.ffill().dropna()
        
        # Add Volume column if missing
        if 'Volume' not in data.columns:
            data['Volume'] = 0
        
        logger.info(f"Market data validated: {len(data)} bars from {data.index[0]} to {data.index[-1]}")
        
        return data
    
    def resample_timeframe(self, target_timeframe: str) -> 'MarketData':
        """
        Resample data to different timeframe.
        
        Args:
            target_timeframe: Target timeframe (e.g., '15m', '1h')
            
        Returns:
            New MarketData instance with resampled data
        """
        # Timeframe mapping
        timeframe_map = {
            '1m': '1T', '5m': '5T', '15m': '15T', '30m': '30T',
            '1h': '1H', '4h': '4H', '1d': '1D'
        }
        
        if target_timeframe not in timeframe_map:
            raise ValueError(f"Unsupported timeframe: {target_timeframe}")
        
        freq = timeframe_map[target_timeframe]
        
        # Resample OHLCV data
        resampled = self._data.resample(freq).agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        return MarketData(resampled, self.symbol, target_timeframe)
    
    def get_data_range(self, start: Optional[datetime] = None, 
                      end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Get data for specific date range.
        
        Args:
            start: Start datetime
            end: End datetime
            
        Returns:
            Filtered DataFrame
        """
        data = self._data.copy()
        
        if start:
            data = data[data.index >= start]
        if end:
            data = data[data.index <= end]
            
        return data
    
    def add_indicator(self, name: str, values: pd.Series) -> None:
        """
        Add technical indicator to the data.
        
        Args:
            name: Indicator name
            values: Indicator values as pandas Series
        """
        if len(values) != len(self._data):
            raise ValueError(f"Indicator length ({len(values)}) doesn't match data length ({len(self._data)})")
        
        self._indicators[name] = values
        logger.debug(f"Added indicator '{name}' to market data")
    
    def get_indicator(self, name: str) -> Optional[pd.Series]:
        """
        Get technical indicator values.
        
        Args:
            name: Indicator name
            
        Returns:
            Indicator values or None if not found
        """
        return self._indicators.get(name)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get basic statistics about the market data.
        
        Returns:
            Dictionary with statistics
        """
        data = self._data
        
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'start_date': data.index[0],
            'end_date': data.index[-1],
            'total_bars': len(data),
            'price_range': {
                'min': data['Low'].min(),
                'max': data['High'].max(),
                'avg_close': data['Close'].mean(),
            },
            'volatility': {
                'daily_returns_std': data['Close'].pct_change().std(),
                'price_range_avg': (data['High'] - data['Low']).mean(),
            },
            'volume': {
                'total': data['Volume'].sum(),
                'average': data['Volume'].mean(),
            } if 'Volume' in data.columns else None
        }
    
    def to_csv(self, filepath: str) -> None:
        """Save market data to CSV file."""
        self._data.to_csv(filepath)
        logger.info(f"Market data saved to {filepath}")
    
    @classmethod
    def from_csv(cls, filepath: str, symbol: str, timeframe: str) -> 'MarketData':
        """
        Load market data from CSV file.
        
        Args:
            filepath: Path to CSV file
            symbol: Trading symbol
            timeframe: Data timeframe
            
        Returns:
            MarketData instance
        """
        data = pd.read_csv(filepath, index_col=0, parse_dates=True)
        return cls(data, symbol, timeframe)
