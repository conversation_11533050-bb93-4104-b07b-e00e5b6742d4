"""
Data loading and preprocessing for Forex backtesting system.
Supports multiple data sources including yfinance, Alpha Vantage, and MetaTrader 5.
"""

import pandas as pd
import numpy as np
import logging
import yfinance as yf
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pathlib import Path

from .market_data import MarketData
from ..config.trading_params import SYMBOL_INFO, DATA_SOURCES

logger = logging.getLogger(__name__)


class DataLoadingError(Exception):
    """Custom exception for data loading errors."""
    pass


class DataLoader:
    """
    Data loader with support for multiple data sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize DataLoader with configuration.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.source = self.data_config.get('source', 'yfinance')
        self.data_path = Path(self.data_config.get('data_path', 'data/'))
        self.data_path.mkdir(parents=True, exist_ok=True)
        
    def load_data(self, symbol: str, start_date: str, end_date: str, 
                  timeframe: str = '5m', force_download: bool = False) -> MarketData:
        """
        Load market data for specified parameters.
        
        Args:
            symbol: Trading symbol (e.g., 'AUDUSD')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            timeframe: Data timeframe (e.g., '5m')
            force_download: Force download even if cached data exists
            
        Returns:
            MarketData instance
            
        Raises:
            DataLoadingError: If data cannot be loaded
        """
        # Check cache first
        cache_file = self._get_cache_filename(symbol, start_date, end_date, timeframe)
        
        if not force_download and cache_file.exists():
            try:
                logger.info(f"Loading cached data from {cache_file}")
                return MarketData.from_csv(str(cache_file), symbol, timeframe)
            except Exception as e:
                logger.warning(f"Failed to load cached data: {e}. Downloading fresh data.")
        
        # Download fresh data
        logger.info(f"Downloading {symbol} data from {start_date} to {end_date} ({timeframe})")
        
        if self.source == 'yfinance':
            data = self._load_from_yfinance(symbol, start_date, end_date, timeframe)
        elif self.source == 'alphavantage':
            data = self._load_from_alphavantage(symbol, start_date, end_date, timeframe)
        elif self.source == 'mt5':
            data = self._load_from_mt5(symbol, start_date, end_date, timeframe)
        else:
            raise DataLoadingError(f"Unsupported data source: {self.source}")
        
        # Create MarketData instance
        market_data = MarketData(data, symbol, timeframe)
        
        # Cache the data
        try:
            market_data.to_csv(str(cache_file))
            logger.info(f"Data cached to {cache_file}")
        except Exception as e:
            logger.warning(f"Failed to cache data: {e}")
        
        return market_data
    
    def _load_from_yfinance(self, symbol: str, start_date: str, 
                           end_date: str, timeframe: str) -> pd.DataFrame:
        """Load data from Yahoo Finance."""
        try:
            # Convert symbol format for yfinance
            yf_symbol = self._convert_symbol_for_yfinance(symbol)
            
            # Convert timeframe
            interval = self._convert_timeframe_for_yfinance(timeframe)
            
            # Download data
            ticker = yf.Ticker(yf_symbol)
            data = ticker.history(
                start=start_date,
                end=end_date,
                interval=interval,
                auto_adjust=True,
                prepost=False
            )
            
            if data.empty:
                raise DataLoadingError(f"No data returned for {symbol}")
            
            # Standardize column names
            data = data.rename(columns={
                'Open': 'Open',
                'High': 'High', 
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            })
            
            return data
            
        except Exception as e:
            raise DataLoadingError(f"Failed to load data from yfinance: {e}")
    
    def _load_from_alphavantage(self, symbol: str, start_date: str,
                               end_date: str, timeframe: str) -> pd.DataFrame:
        """Load data from Alpha Vantage (placeholder implementation)."""
        # This would require Alpha Vantage API implementation
        raise DataLoadingError("Alpha Vantage data source not yet implemented")
    
    def _load_from_mt5(self, symbol: str, start_date: str,
                      end_date: str, timeframe: str) -> pd.DataFrame:
        """Load data from MetaTrader 5 (placeholder implementation)."""
        # This would require MetaTrader 5 API implementation
        raise DataLoadingError("MetaTrader 5 data source not yet implemented")
    
    def _convert_symbol_for_yfinance(self, symbol: str) -> str:
        """Convert symbol format for yfinance."""
        # Yahoo Finance uses different symbol formats
        symbol_map = {
            'AUDUSD': 'AUDUSD=X',
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X',
            'USDJPY': 'USDJPY=X',
            'USDCHF': 'USDCHF=X',
            'USDCAD': 'USDCAD=X',
            'NZDUSD': 'NZDUSD=X',
        }
        return symbol_map.get(symbol, symbol)
    
    def _convert_timeframe_for_yfinance(self, timeframe: str) -> str:
        """Convert timeframe format for yfinance."""
        timeframe_map = {
            '1m': '1m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '1d': '1d'
        }
        
        if timeframe not in timeframe_map:
            raise DataLoadingError(f"Unsupported timeframe for yfinance: {timeframe}")
        
        return timeframe_map[timeframe]
    
    def _get_cache_filename(self, symbol: str, start_date: str, 
                           end_date: str, timeframe: str) -> Path:
        """Generate cache filename for data."""
        filename = f"{symbol}_{timeframe}_{start_date}_{end_date}.csv"
        return self.data_path / filename
    
    def get_available_symbols(self) -> List[str]:
        """Get list of supported symbols."""
        return list(SYMBOL_INFO.keys())
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is supported."""
        return symbol in SYMBOL_INFO
    
    def validate_timeframe(self, timeframe: str) -> bool:
        """Validate if timeframe is supported for current data source."""
        source_config = DATA_SOURCES.get(self.source, {})
        supported_timeframes = source_config.get('supported_timeframes', [])
        return timeframe in supported_timeframes
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get symbol information including pip value and digits."""
        return SYMBOL_INFO.get(symbol, {})
    
    def clear_cache(self, symbol: Optional[str] = None) -> None:
        """
        Clear cached data files.
        
        Args:
            symbol: If specified, only clear cache for this symbol
        """
        if symbol:
            pattern = f"{symbol}_*.csv"
        else:
            pattern = "*.csv"
        
        cache_files = list(self.data_path.glob(pattern))
        
        for file in cache_files:
            try:
                file.unlink()
                logger.info(f"Removed cache file: {file}")
            except Exception as e:
                logger.warning(f"Failed to remove cache file {file}: {e}")
        
        logger.info(f"Cleared {len(cache_files)} cache files")
    
    def preload_data(self, symbols: List[str], start_date: str, 
                    end_date: str, timeframes: List[str]) -> Dict[str, Dict[str, MarketData]]:
        """
        Preload data for multiple symbols and timeframes.
        
        Args:
            symbols: List of symbols to load
            start_date: Start date
            end_date: End date
            timeframes: List of timeframes
            
        Returns:
            Nested dictionary: {symbol: {timeframe: MarketData}}
        """
        data_cache = {}
        
        for symbol in symbols:
            if not self.validate_symbol(symbol):
                logger.warning(f"Skipping unsupported symbol: {symbol}")
                continue
                
            data_cache[symbol] = {}
            
            for timeframe in timeframes:
                if not self.validate_timeframe(timeframe):
                    logger.warning(f"Skipping unsupported timeframe {timeframe} for {self.source}")
                    continue
                
                try:
                    market_data = self.load_data(symbol, start_date, end_date, timeframe)
                    data_cache[symbol][timeframe] = market_data
                    logger.info(f"Preloaded {symbol} {timeframe} data")
                    
                except Exception as e:
                    logger.error(f"Failed to preload {symbol} {timeframe}: {e}")
        
        return data_cache
