"""
Local CSV file loader for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path
import glob
import re

from .market_data import MarketData
from ..config.trading_params import LOCAL_FILE_PATTERNS, DATETIME_COLUMNS, REQUIRED_OHLC_COLUMNS

logger = logging.getLogger(__name__)


class LocalDataLoadingError(Exception):
    """Custom exception for local data loading errors."""
    pass


class LocalDataLoader:
    """
    Local CSV file loader with support for multiple file formats and naming conventions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize local data loader.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.data_config = config.get('data', {})
        
        # Local data settings
        self.local_data_path = Path(self.data_config.get('local_data_path', 'data/local/'))
        self.file_pattern = self.data_config.get('local_file_pattern', '{symbol}_{timeframe}.csv')
        self.datetime_column = self.data_config.get('datetime_column', 'Date')
        self.timezone = self.data_config.get('timezone', 'UTC')
        self.fallback_to_online = self.data_config.get('fallback_to_online', True)
        
        # Ensure local data directory exists
        self.local_data_path.mkdir(parents=True, exist_ok=True)
        
    def load_data(self, symbol: str, start_date: str, end_date: str, 
                  timeframe: str = '5m') -> MarketData:
        """
        Load market data from local CSV file.
        
        Args:
            symbol: Trading symbol (e.g., 'AUDUSD')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            timeframe: Data timeframe (e.g., '5m')
            
        Returns:
            MarketData instance
            
        Raises:
            LocalDataLoadingError: If data cannot be loaded
        """
        try:
            # Find the appropriate CSV file
            csv_file = self._find_csv_file(symbol, timeframe)

            if not csv_file:
                # Provide helpful error message with suggestions
                available_files = self.list_available_files()
                error_msg = f"No local data file found for {symbol} {timeframe}"

                if available_files:
                    symbols = set(f['symbol'] for f in available_files if f['symbol'] != 'unknown')
                    timeframes = set(f['timeframe'] for f in available_files if f['timeframe'] != 'unknown')

                    if symbols:
                        error_msg += f"\nAvailable symbols: {', '.join(sorted(symbols))}"
                    if timeframes:
                        error_msg += f"\nAvailable timeframes: {', '.join(sorted(timeframes))}"

                    error_msg += f"\nExpected filename pattern: {self.file_pattern.format(symbol=symbol, timeframe=timeframe)}"
                    error_msg += f"\nLocal data directory: {self.local_data_path}"
                else:
                    error_msg += f"\nNo CSV files found in directory: {self.local_data_path}"
                    error_msg += "\nPlease ensure your CSV files are in the correct directory with proper naming."

                raise LocalDataLoadingError(error_msg)
            
            logger.info(f"Loading local data from {csv_file}")
            
            # Load and validate CSV data
            data = self._load_csv_file(csv_file)
            
            # Filter by date range
            data = self._filter_date_range(data, start_date, end_date)
            
            if data.empty:
                raise LocalDataLoadingError(f"No data found in date range {start_date} to {end_date}")
            
            # Create MarketData instance
            market_data = MarketData(data, symbol, timeframe)
            
            logger.info(f"Loaded {len(data)} bars from local file")
            return market_data
            
        except LocalDataLoadingError:
            # Re-raise LocalDataLoadingError as-is
            raise
        except Exception as e:
            logger.error(f"Failed to load local data: {e}")
            raise LocalDataLoadingError(f"Local data loading failed: {e}")
    
    def _find_csv_file(self, symbol: str, timeframe: str) -> Optional[Path]:
        """
        Find CSV file for given symbol and timeframe.
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            
        Returns:
            Path to CSV file or None if not found
        """
        # Try exact pattern match first
        filename = self.file_pattern.format(symbol=symbol, timeframe=timeframe)
        exact_file = self.local_data_path / filename
        
        if exact_file.exists():
            return exact_file
        
        # Try alternative patterns
        alternative_patterns = [
            f"{symbol}_{timeframe}.csv",
            f"{symbol}{timeframe}.csv",
            f"{symbol}_{timeframe}_*.csv",
            f"{symbol}_*.csv",
            f"*{symbol}*.csv"
        ]
        
        for pattern in alternative_patterns:
            files = list(self.local_data_path.glob(pattern))
            if files:
                # Return the first match
                logger.info(f"Found alternative file: {files[0]}")
                return files[0]
        
        # Try case-insensitive search
        symbol_lower = symbol.lower()
        for file in self.local_data_path.glob("*.csv"):
            if symbol_lower in file.name.lower() and timeframe in file.name:
                logger.info(f"Found case-insensitive match: {file}")
                return file
        
        return None
    
    def _load_csv_file(self, csv_file: Path) -> pd.DataFrame:
        """
        Load and validate CSV file.
        
        Args:
            csv_file: Path to CSV file
            
        Returns:
            Validated DataFrame with datetime index
            
        Raises:
            LocalDataLoadingError: If file cannot be loaded or validated
        """
        try:
            # Try different CSV reading options
            read_options = [
                {'sep': ',', 'encoding': 'utf-8'},
                {'sep': ';', 'encoding': 'utf-8'},
                {'sep': '\t', 'encoding': 'utf-8'},
                {'sep': ',', 'encoding': 'latin-1'},
            ]
            
            data = None
            for options in read_options:
                try:
                    data = pd.read_csv(csv_file, **options)
                    if len(data) > 0:
                        break
                except Exception:
                    continue
            
            if data is None or data.empty:
                raise LocalDataLoadingError(f"Could not read CSV file: {csv_file}")
            
            logger.debug(f"Loaded CSV with {len(data)} rows and columns: {list(data.columns)}")
            
            # Validate and process the data
            data = self._validate_and_process_data(data)
            
            return data
            
        except Exception as e:
            raise LocalDataLoadingError(f"Error loading CSV file {csv_file}: {e}")
    
    def _validate_and_process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and process loaded CSV data.
        
        Args:
            data: Raw DataFrame from CSV
            
        Returns:
            Processed DataFrame with datetime index and standardized columns
            
        Raises:
            LocalDataLoadingError: If validation fails
        """
        # Find datetime column
        datetime_col = self._find_datetime_column(data)
        if not datetime_col:
            raise LocalDataLoadingError("No datetime column found in CSV file")
        
        # Convert datetime column
        try:
            data[datetime_col] = pd.to_datetime(data[datetime_col])
            data = data.set_index(datetime_col)
            data.index.name = 'Date'
        except Exception as e:
            raise LocalDataLoadingError(f"Error converting datetime column: {e}")
        
        # Find and standardize OHLC columns
        column_mapping = self._find_ohlc_columns(data)
        if not column_mapping:
            raise LocalDataLoadingError("Required OHLC columns not found in CSV file")
        
        # Rename columns to standard format
        data = data.rename(columns=column_mapping)
        
        # Ensure we have all required columns
        missing_cols = [col for col in REQUIRED_OHLC_COLUMNS if col not in data.columns]
        if missing_cols:
            raise LocalDataLoadingError(f"Missing required columns: {missing_cols}")
        
        # Add Volume column if missing
        if 'Volume' not in data.columns:
            data['Volume'] = 0
            logger.info("Added default Volume column (all zeros)")
        
        # Sort by datetime
        data = data.sort_index()
        
        # Remove duplicates
        data = data[~data.index.duplicated(keep='first')]
        
        # Validate OHLC relationships
        self._validate_ohlc_data(data)
        
        # Handle missing values
        if data.isnull().any().any():
            logger.warning("Found missing values in data, forward filling...")
            data = data.ffill().dropna()
        
        logger.info(f"Processed data: {len(data)} bars from {data.index[0]} to {data.index[-1]}")
        
        return data
    
    def _find_datetime_column(self, data: pd.DataFrame) -> Optional[str]:
        """Find datetime column in DataFrame."""
        # Check for exact matches first
        for col_name in DATETIME_COLUMNS:
            if col_name in data.columns:
                return col_name
        
        # Check for case-insensitive matches
        for col in data.columns:
            for datetime_col in DATETIME_COLUMNS:
                if col.lower() == datetime_col.lower():
                    return col
        
        # Check if first column looks like datetime
        if len(data.columns) > 0:
            first_col = data.columns[0]
            try:
                pd.to_datetime(data[first_col].iloc[0])
                logger.info(f"Using first column '{first_col}' as datetime")
                return first_col
            except Exception:
                pass
        
        return None
    
    def _find_ohlc_columns(self, data: pd.DataFrame) -> Dict[str, str]:
        """
        Find OHLC columns in DataFrame.
        
        Returns:
            Dictionary mapping found columns to standard names
        """
        column_mapping = {}
        
        # Define possible column name variations
        column_variations = {
            'Open': ['open', 'o', 'Open', 'OPEN'],
            'High': ['high', 'h', 'High', 'HIGH'],
            'Low': ['low', 'l', 'Low', 'LOW'],
            'Close': ['close', 'c', 'Close', 'CLOSE', 'Adj Close', 'adj_close'],
            'Volume': ['volume', 'v', 'Volume', 'VOLUME', 'Vol', 'vol']
        }
        
        for standard_name, variations in column_variations.items():
            for variation in variations:
                if variation in data.columns:
                    column_mapping[variation] = standard_name
                    break
        
        return column_mapping
    
    def _validate_ohlc_data(self, data: pd.DataFrame) -> None:
        """Validate OHLC data relationships."""
        # Check for invalid OHLC relationships
        invalid_ohlc = (
            (data['High'] < data['Low']) |
            (data['High'] < data['Open']) |
            (data['High'] < data['Close']) |
            (data['Low'] > data['Open']) |
            (data['Low'] > data['Close'])
        )
        
        if invalid_ohlc.any():
            invalid_count = invalid_ohlc.sum()
            logger.warning(f"Found {invalid_count} rows with invalid OHLC relationships")
            
            # Remove invalid rows
            data = data[~invalid_ohlc]
    
    def _filter_date_range(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """Filter data by date range."""
        try:
            start = pd.to_datetime(start_date)
            end = pd.to_datetime(end_date)
            
            # Check if data index has timezone info
            if data.index.tz is not None:
                # Option 1: Make filter dates timezone-aware
                start = start.tz_localize(data.index.tz, nonexistent='shift_forward')
                end = end.tz_localize(data.index.tz, nonexistent='shift_forward')
                
                # Option 2 (alternative): Make data index timezone-naive
                # data = data.copy()
                # data.index = data.index.tz_localize(None)
            
            # Filter data
            mask = (data.index >= start) & (data.index <= end)
            filtered_data = data[mask]
            
            logger.info(f"Filtered data from {start_date} to {end_date}: {len(filtered_data)} bars")
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"Error filtering date range: {e}")
            return data
    
    def list_available_files(self) -> List[Dict[str, str]]:
        """
        List all available CSV files in the local data directory.
        
        Returns:
            List of dictionaries with file information
        """
        files = []
        
        for csv_file in self.local_data_path.glob("*.csv"):
            try:
                # Try to extract symbol and timeframe from filename
                symbol, timeframe = self._parse_filename(csv_file.name)
                
                files.append({
                    'filename': csv_file.name,
                    'path': str(csv_file),
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'size': csv_file.stat().st_size,
                    'modified': datetime.fromtimestamp(csv_file.stat().st_mtime)
                })
                
            except Exception as e:
                logger.debug(f"Could not parse file {csv_file.name}: {e}")
        
        return files
    
    def _parse_filename(self, filename: str) -> Tuple[str, str]:
        """
        Parse symbol and timeframe from filename.
        
        Args:
            filename: CSV filename
            
        Returns:
            Tuple of (symbol, timeframe)
        """
        # Remove .csv extension
        name = filename.replace('.csv', '')
        
        # Try common patterns
        patterns = [
            r'([A-Z]{6})_(\d+[mhd])',  # AUDUSD_5m
            r'([A-Z]{6})(\d+[mhd])',   # AUDUSD5m
            r'([A-Z]{6})_(\d+)M',      # AUDUSD_5M
            r'([A-Z]{6})(\d+)M',       # AUDUSD5M
        ]
        
        for pattern in patterns:
            match = re.match(pattern, name, re.IGNORECASE)
            if match:
                symbol = match.group(1).upper()
                timeframe = match.group(2).lower()
                return symbol, timeframe
        
        # Default fallback
        return name.upper(), 'unknown'
