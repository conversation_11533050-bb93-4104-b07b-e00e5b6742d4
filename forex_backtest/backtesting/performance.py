"""
Performance analysis for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from scipy import stats

logger = logging.getLogger(__name__)


class PerformanceAnalyzer:
    """
    Comprehensive performance analysis for backtesting results.
    """
    
    def __init__(self, results):
        """
        Initialize performance analyzer.
        
        Args:
            results: BacktestResults object
        """
        self.results = results
        self.portfolio = results.portfolio
        self.trade_history = results.trade_history
        self.equity_curve = results.equity_curve
        
    def calculate_advanced_metrics(self) -> Dict[str, Any]:
        """Calculate advanced performance metrics."""
        metrics = {}
        
        # Basic metrics from portfolio
        basic_metrics = self.portfolio.get_performance_summary()
        metrics.update(basic_metrics)
        
        # Additional risk metrics
        metrics.update(self._calculate_risk_metrics())
        
        # Trade analysis
        metrics.update(self._analyze_trades())
        
        # Drawdown analysis
        metrics.update(self._analyze_drawdowns())
        
        # Time-based analysis
        metrics.update(self._analyze_time_performance())
        
        # Martingale analysis
        metrics.update(self._analyze_martingale_usage())
        
        return metrics
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk-related metrics."""
        if self.equity_curve.empty:
            return {}
        
        equity_series = self.equity_curve['equity']
        returns = equity_series.pct_change().dropna()
        
        if len(returns) == 0:
            return {}
        
        # Value at Risk (VaR)
        var_95 = np.percentile(returns, 5) * 100
        var_99 = np.percentile(returns, 1) * 100
        
        # Conditional Value at Risk (CVaR)
        cvar_95 = returns[returns <= np.percentile(returns, 5)].mean() * 100
        cvar_99 = returns[returns <= np.percentile(returns, 1)].mean() * 100
        
        # Sortino Ratio
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = (returns.mean() * 252 - 0.02) / downside_deviation if downside_deviation > 0 else 0
        
        # Maximum Adverse Excursion (MAE) and Maximum Favorable Excursion (MFE)
        mae, mfe = self._calculate_mae_mfe()
        
        return {
            'var_95_percent': var_95,
            'var_99_percent': var_99,
            'cvar_95_percent': cvar_95,
            'cvar_99_percent': cvar_99,
            'sortino_ratio': sortino_ratio,
            'downside_deviation': downside_deviation,
            'max_adverse_excursion': mae,
            'max_favorable_excursion': mfe,
            'volatility_annual': returns.std() * np.sqrt(252) * 100
        }
    
    def _analyze_trades(self) -> Dict[str, Any]:
        """Analyze individual trades."""
        if not self.trade_history:
            return {}
        
        trades_df = pd.DataFrame(self.trade_history)
        
        # Trade duration analysis
        if 'entry_timestamp' in trades_df.columns and 'exit_timestamp' in trades_df.columns:
            trades_df['duration'] = pd.to_datetime(trades_df['exit_timestamp']) - pd.to_datetime(trades_df['entry_timestamp'])
            avg_duration = trades_df['duration'].mean()
            max_duration = trades_df['duration'].max()
            min_duration = trades_df['duration'].min()
        else:
            avg_duration = max_duration = min_duration = timedelta(0)
        
        # PnL analysis
        pnls = trades_df['pnl'].values
        winning_trades = pnls[pnls > 0]
        losing_trades = pnls[pnls < 0]
        
        # Consecutive wins/losses
        consecutive_wins, consecutive_losses = self._calculate_consecutive_trades(pnls)
        
        # Trade size analysis
        if 'total_size' in trades_df.columns:
            avg_trade_size = trades_df['total_size'].mean()
            max_trade_size = trades_df['total_size'].max()
        else:
            avg_trade_size = max_trade_size = 0
        
        return {
            'average_trade_duration': avg_duration,
            'max_trade_duration': max_duration,
            'min_trade_duration': min_duration,
            'largest_winning_trade': max(winning_trades) if len(winning_trades) > 0 else 0,
            'largest_losing_trade': min(losing_trades) if len(losing_trades) > 0 else 0,
            'consecutive_wins_max': consecutive_wins,
            'consecutive_losses_max': consecutive_losses,
            'average_trade_size': avg_trade_size,
            'max_trade_size': max_trade_size,
            'expectancy': np.mean(pnls) if len(pnls) > 0 else 0
        }
    
    def _analyze_drawdowns(self) -> Dict[str, Any]:
        """Analyze drawdown characteristics."""
        if self.equity_curve.empty:
            return {}
        
        equity_series = self.equity_curve['equity']
        
        # Calculate running maximum
        running_max = equity_series.expanding().max()
        drawdown_series = (equity_series - running_max) / running_max * 100
        
        # Find drawdown periods
        drawdown_periods = self._find_drawdown_periods(drawdown_series)
        
        if not drawdown_periods:
            return {
                'max_drawdown_duration': timedelta(0),
                'average_drawdown_duration': timedelta(0),
                'drawdown_frequency': 0
            }
        
        durations = [period['duration'] for period in drawdown_periods]
        
        return {
            'max_drawdown_duration': max(durations),
            'average_drawdown_duration': np.mean(durations),
            'drawdown_frequency': len(drawdown_periods),
            'recovery_factor': abs(self.portfolio.total_realized_pnl / self.portfolio.max_drawdown) if self.portfolio.max_drawdown != 0 else 0
        }
    
    def _analyze_time_performance(self) -> Dict[str, Any]:
        """Analyze performance by time periods."""
        if self.equity_curve.empty:
            return {}
        
        equity_series = self.equity_curve['equity']
        
        # Monthly analysis
        monthly_returns = equity_series.resample('M').last().pct_change().dropna() * 100
        
        # Weekly analysis
        weekly_returns = equity_series.resample('W').last().pct_change().dropna() * 100
        
        # Best/worst periods
        best_month = monthly_returns.max() if len(monthly_returns) > 0 else 0
        worst_month = monthly_returns.min() if len(monthly_returns) > 0 else 0
        best_week = weekly_returns.max() if len(weekly_returns) > 0 else 0
        worst_week = weekly_returns.min() if len(weekly_returns) > 0 else 0
        
        # Consistency metrics
        positive_months = (monthly_returns > 0).sum() if len(monthly_returns) > 0 else 0
        total_months = len(monthly_returns)
        
        return {
            'best_month_percent': best_month,
            'worst_month_percent': worst_month,
            'best_week_percent': best_week,
            'worst_week_percent': worst_week,
            'positive_months': positive_months,
            'total_months': total_months,
            'monthly_win_rate': (positive_months / total_months * 100) if total_months > 0 else 0
        }
    
    def _analyze_martingale_usage(self) -> Dict[str, Any]:
        """Analyze martingale system usage."""
        if not self.trade_history:
            return {}
        
        trades_df = pd.DataFrame(self.trade_history)
        
        if 'martingale_orders' not in trades_df.columns:
            return {}
        
        martingale_counts = trades_df['martingale_orders'].values
        
        # Martingale statistics
        trades_with_martingale = (martingale_counts > 0).sum()
        avg_martingale_per_trade = martingale_counts.mean()
        max_martingale_used = martingale_counts.max()
        
        # Performance by martingale level
        martingale_performance = {}
        for level in range(int(max_martingale_used) + 1):
            level_trades = trades_df[trades_df['martingale_orders'] == level]
            if len(level_trades) > 0:
                martingale_performance[f'level_{level}'] = {
                    'count': len(level_trades),
                    'avg_pnl': level_trades['pnl'].mean(),
                    'win_rate': (level_trades['pnl'] > 0).mean() * 100
                }
        
        return {
            'trades_with_martingale': trades_with_martingale,
            'martingale_usage_rate': (trades_with_martingale / len(trades_df) * 100) if len(trades_df) > 0 else 0,
            'avg_martingale_per_trade': avg_martingale_per_trade,
            'max_martingale_used': max_martingale_used,
            'martingale_performance_by_level': martingale_performance
        }
    
    def _calculate_mae_mfe(self) -> Tuple[float, float]:
        """Calculate Maximum Adverse Excursion and Maximum Favorable Excursion."""
        # This would require tick-by-tick data to calculate properly
        # For now, return placeholder values
        return 0.0, 0.0
    
    def _calculate_consecutive_trades(self, pnls: np.ndarray) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if len(pnls) == 0:
            return 0, 0
        
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_consecutive_wins = 0
        current_consecutive_losses = 0
        
        for pnl in pnls:
            if pnl > 0:
                current_consecutive_wins += 1
                current_consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_consecutive_wins)
            elif pnl < 0:
                current_consecutive_losses += 1
                current_consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)
            else:
                current_consecutive_wins = 0
                current_consecutive_losses = 0
        
        return max_consecutive_wins, max_consecutive_losses
    
    def _find_drawdown_periods(self, drawdown_series: pd.Series) -> List[Dict[str, Any]]:
        """Find individual drawdown periods."""
        periods = []
        in_drawdown = False
        start_date = None
        
        for date, dd in drawdown_series.items():
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                start_date = date
            elif dd >= 0 and in_drawdown:
                # End of drawdown
                in_drawdown = False
                if start_date:
                    periods.append({
                        'start': start_date,
                        'end': date,
                        'duration': date - start_date
                    })
        
        # Handle case where drawdown continues to end
        if in_drawdown and start_date:
            periods.append({
                'start': start_date,
                'end': drawdown_series.index[-1],
                'duration': drawdown_series.index[-1] - start_date
            })
        
        return periods
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            'summary': self.calculate_advanced_metrics(),
            'equity_curve': self.equity_curve.to_dict('records') if not self.equity_curve.empty else [],
            'trade_history': self.trade_history,
            'monthly_returns': self.portfolio.get_monthly_returns().to_dict() if hasattr(self.portfolio, 'get_monthly_returns') else {}
        }
