"""
Portfolio management for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PortfolioSnapshot:
    """Portfolio state at a specific point in time."""
    timestamp: datetime
    balance: float
    equity: float
    unrealized_pnl: float
    realized_pnl: float
    total_positions: int
    total_exposure: float
    margin_used: float
    free_margin: float
    margin_level: float


class Portfolio:
    """
    Portfolio manager for tracking account state and performance.
    """
    
    def __init__(self, initial_balance: float, leverage: float = 100.0,
                 margin_requirement: float = 0.01):
        """
        Initialize portfolio.
        
        Args:
            initial_balance: Starting account balance
            leverage: Account leverage
            margin_requirement: Margin requirement (1% = 0.01)
        """
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = leverage
        self.margin_requirement = margin_requirement
        
        # Portfolio tracking
        self.equity_curve = []
        self.balance_curve = []
        self.drawdown_curve = []
        self.snapshots = []
        
        # Performance metrics
        self.peak_equity = initial_balance
        self.max_drawdown = 0.0
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
        
        # Position tracking
        self.open_positions = {}
        self.closed_positions = []
        
    def update(self, timestamp: datetime, positions: Dict[str, Any],
               realized_pnl: float = 0.0) -> None:
        """
        Update portfolio state.
        
        Args:
            timestamp: Current timestamp
            positions: Dictionary of open positions
            realized_pnl: Realized PnL from closed positions
        """
        # Update balance with realized PnL
        if realized_pnl != 0:
            self.current_balance += realized_pnl
            self.total_realized_pnl += realized_pnl
        
        # Calculate unrealized PnL from open positions
        unrealized_pnl = sum(pos.unrealized_pnl for pos in positions.values())
        self.total_unrealized_pnl = unrealized_pnl
        
        # Calculate equity
        equity = self.current_balance + unrealized_pnl
        
        # Calculate exposure and margin
        total_exposure = sum(pos.total_size * pos.current_price for pos in positions.values())
        margin_used = total_exposure * self.margin_requirement
        free_margin = equity - margin_used
        margin_level = (equity / margin_used * 100) if margin_used > 0 else float('inf')
        
        # Update peak equity and drawdown
        if equity > self.peak_equity:
            self.peak_equity = equity
        
        current_drawdown = (self.peak_equity - equity) / self.peak_equity * 100
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Store curves
        self.equity_curve.append(equity)
        self.balance_curve.append(self.current_balance)
        self.drawdown_curve.append(current_drawdown)
        
        # Create snapshot
        snapshot = PortfolioSnapshot(
            timestamp=timestamp,
            balance=self.current_balance,
            equity=equity,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=self.total_realized_pnl,
            total_positions=len(positions),
            total_exposure=total_exposure,
            margin_used=margin_used,
            free_margin=free_margin,
            margin_level=margin_level
        )
        
        self.snapshots.append(snapshot)
        
        # Update position tracking
        self.open_positions = positions.copy()
    
    def add_closed_position(self, position_data: Dict[str, Any]) -> None:
        """Add a closed position to history."""
        self.closed_positions.append(position_data)
    
    def get_current_equity(self) -> float:
        """Get current equity."""
        return self.current_balance + self.total_unrealized_pnl
    
    def get_total_return(self) -> float:
        """Get total return percentage."""
        current_equity = self.get_current_equity()
        return (current_equity - self.initial_balance) / self.initial_balance * 100
    
    def get_max_drawdown(self) -> float:
        """Get maximum drawdown percentage."""
        return self.max_drawdown
    
    def get_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """
        Calculate Sharpe ratio.
        
        Args:
            risk_free_rate: Risk-free rate (annual)
            
        Returns:
            Sharpe ratio
        """
        if len(self.equity_curve) < 2:
            return 0.0
        
        # Calculate daily returns
        equity_series = pd.Series(self.equity_curve)
        daily_returns = equity_series.pct_change().dropna()
        
        if len(daily_returns) == 0 or daily_returns.std() == 0:
            return 0.0
        
        # Annualize returns (assuming daily data)
        annual_return = daily_returns.mean() * 252
        annual_volatility = daily_returns.std() * np.sqrt(252)
        
        return (annual_return - risk_free_rate) / annual_volatility
    
    def get_calmar_ratio(self) -> float:
        """Calculate Calmar ratio (Annual Return / Max Drawdown)."""
        if self.max_drawdown == 0:
            return float('inf')
        
        annual_return = self.get_annualized_return()
        return annual_return / (self.max_drawdown / 100)
    
    def get_annualized_return(self) -> float:
        """Calculate annualized return."""
        if len(self.snapshots) < 2:
            return 0.0
        
        # Calculate time period in years
        start_date = self.snapshots[0].timestamp
        end_date = self.snapshots[-1].timestamp
        days = (end_date - start_date).days
        
        if days == 0:
            return 0.0
        
        years = days / 365.25
        
        # Calculate annualized return
        total_return = self.get_total_return() / 100
        return ((1 + total_return) ** (1 / years) - 1) * 100
    
    def get_win_rate(self) -> float:
        """Calculate win rate from closed positions."""
        if not self.closed_positions:
            return 0.0
        
        winning_trades = sum(1 for pos in self.closed_positions if pos.get('pnl', 0) > 0)
        return winning_trades / len(self.closed_positions) * 100
    
    def get_profit_factor(self) -> float:
        """Calculate profit factor (Gross Profit / Gross Loss)."""
        if not self.closed_positions:
            return 0.0
        
        gross_profit = sum(pos.get('pnl', 0) for pos in self.closed_positions if pos.get('pnl', 0) > 0)
        gross_loss = abs(sum(pos.get('pnl', 0) for pos in self.closed_positions if pos.get('pnl', 0) < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def get_average_trade(self) -> Dict[str, float]:
        """Get average trade statistics."""
        if not self.closed_positions:
            return {'average': 0.0, 'average_win': 0.0, 'average_loss': 0.0}
        
        pnls = [pos.get('pnl', 0) for pos in self.closed_positions]
        winning_pnls = [pnl for pnl in pnls if pnl > 0]
        losing_pnls = [pnl for pnl in pnls if pnl < 0]
        
        return {
            'average': np.mean(pnls),
            'average_win': np.mean(winning_pnls) if winning_pnls else 0.0,
            'average_loss': np.mean(losing_pnls) if losing_pnls else 0.0
        }
    
    def get_equity_curve_dataframe(self) -> pd.DataFrame:
        """Get equity curve as DataFrame."""
        if not self.snapshots:
            return pd.DataFrame()
        
        data = {
            'timestamp': [s.timestamp for s in self.snapshots],
            'balance': [s.balance for s in self.snapshots],
            'equity': [s.equity for s in self.snapshots],
            'unrealized_pnl': [s.unrealized_pnl for s in self.snapshots],
            'drawdown': self.drawdown_curve,
            'total_positions': [s.total_positions for s in self.snapshots]
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        return df
    
    def get_monthly_returns(self) -> pd.DataFrame:
        """Get monthly returns breakdown."""
        equity_df = self.get_equity_curve_dataframe()
        
        if equity_df.empty:
            return pd.DataFrame()
        
        # Resample to monthly
        monthly_equity = equity_df['equity'].resample('M').last()
        monthly_returns = monthly_equity.pct_change().dropna() * 100
        
        # Create monthly returns table
        monthly_df = pd.DataFrame(monthly_returns)
        monthly_df['year'] = monthly_df.index.year
        monthly_df['month'] = monthly_df.index.month
        
        # Pivot to create year x month table
        pivot_df = monthly_df.pivot(index='year', columns='month', values='equity')
        
        # Add yearly totals
        if len(monthly_equity) > 1:
            yearly_returns = monthly_equity.resample('Y').last().pct_change().dropna() * 100
            if len(yearly_returns) > 0 and len(pivot_df) > 0:
                pivot_df['Total'] = yearly_returns.values[:len(pivot_df)]
        
        return pivot_df
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        return {
            'initial_balance': self.initial_balance,
            'final_balance': self.current_balance,
            'final_equity': self.get_current_equity(),
            'total_return_percent': self.get_total_return(),
            'annualized_return_percent': self.get_annualized_return(),
            'max_drawdown_percent': self.max_drawdown,
            'sharpe_ratio': self.get_sharpe_ratio(),
            'calmar_ratio': self.get_calmar_ratio(),
            'total_trades': len(self.closed_positions),
            'win_rate_percent': self.get_win_rate(),
            'profit_factor': self.get_profit_factor(),
            'total_realized_pnl': self.total_realized_pnl,
            'average_trade': self.get_average_trade(),
            'peak_equity': self.peak_equity
        }
