"""
Main backtesting engine for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from tqdm import tqdm

from .portfolio import Portfolio
from ..strategy.base_strategy import BaseStrategy, Order, OrderStatus, OrderType
from ..data.market_data import MarketData
from ..config.trading_params import SYMBOL_INFO

logger = logging.getLogger(__name__)


class BacktestResults:
    """Container for backtest results."""
    
    def __init__(self, strategy: BaseStrategy, portfolio: Portfolio, 
                 market_data: MarketData, config: Dict[str, Any]):
        """
        Initialize backtest results.
        
        Args:
            strategy: Trading strategy used
            portfolio: Portfolio manager
            market_data: Market data used
            config: Backtest configuration
        """
        self.strategy = strategy
        self.portfolio = portfolio
        self.market_data = market_data
        self.config = config
        
        # Performance metrics
        self.performance_summary = portfolio.get_performance_summary()
        self.trade_history = strategy.get_trade_history()
        self.equity_curve = portfolio.get_equity_curve_dataframe()
        self.monthly_returns = portfolio.get_monthly_returns()
        
        # Strategy-specific data
        self.signals_df = getattr(strategy, 'signals_df', None)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert results to dictionary."""
        return {
            'performance_summary': self.performance_summary,
            'trade_count': len(self.trade_history),
            'config': self.config,
            'start_date': self.market_data.data.index[0],
            'end_date': self.market_data.data.index[-1],
            'symbol': self.market_data.symbol,
            'timeframe': self.market_data.timeframe
        }


class BacktestEngine:
    """
    Main backtesting engine with event-driven architecture.
    """
    
    def __init__(self, strategy: BaseStrategy, market_data: MarketData, 
                 config: Dict[str, Any]):
        """
        Initialize backtest engine.
        
        Args:
            strategy: Trading strategy to test
            market_data: Market data for backtesting
            config: Backtest configuration
        """
        self.strategy = strategy
        self.market_data = market_data
        self.config = config
        
        # Backtest settings
        self.backtest_config = config.get('backtesting', {})
        self.commission = self.backtest_config.get('commission', 0.0002)
        self.spread = self.backtest_config.get('spread', 0.00015)
        self.slippage = self.backtest_config.get('slippage', 0.00005)
        
        # Initialize portfolio
        initial_balance = config.get('trading', {}).get('initial_balance', 10000)
        self.portfolio = Portfolio(initial_balance)
        
        # Symbol information
        symbol = config.get('trading', {}).get('symbol', 'AUDUSD')
        self.symbol_info = SYMBOL_INFO.get(symbol, {})
        self.pip_value = self.symbol_info.get('pip_value', 0.0001)
        
        # Execution tracking
        self.pending_orders = {}
        self.execution_log = []

        # Set up portfolio callback for strategy
        if hasattr(strategy, 'set_portfolio_callback'):
            strategy.set_portfolio_callback(self.portfolio.add_closed_position)
        
    def run(self) -> BacktestResults:
        """
        Run the backtest.
        
        Returns:
            BacktestResults object containing all results
        """
        logger.info("Starting backtest...")
        
        # Get market data
        data = self.market_data.data
        
        # Generate signals
        logger.info("Generating trading signals...")
        signals_df = self.strategy.generate_signals(data)
        
        # Validate data alignment
        if len(signals_df) != len(data):
            logger.warning(f"Signals length ({len(signals_df)}) != Data length ({len(data)})")
            # Align data
            common_index = data.index.intersection(signals_df.index)
            data = data.loc[common_index]
            signals_df = signals_df.loc[common_index]
        
        # Run event-driven simulation
        logger.info(f"Running backtest on {len(data)} bars...")
        
        with tqdm(total=len(data), desc="Backtesting") as pbar:
            for i, (timestamp, bar_data) in enumerate(data.iterrows()):
                # Get current signals
                current_signals = signals_df.loc[timestamp] if timestamp in signals_df.index else pd.Series()
                
                # Process bar
                self._process_bar(bar_data, i, current_signals, timestamp)
                
                pbar.update(1)
        
        # Close any remaining open positions
        self._close_all_positions(data.iloc[-1]['Close'], data.index[-1])
        
        # Create results
        results = BacktestResults(self.strategy, self.portfolio, self.market_data, self.config)
        
        logger.info("Backtest completed successfully")
        self._log_summary(results)
        
        return results
    
    def _process_bar(self, bar_data: pd.Series, bar_index: int, 
                    signals: pd.Series, timestamp: datetime) -> None:
        """
        Process a single bar of data.
        
        Args:
            bar_data: Current bar OHLC data
            bar_index: Bar index
            signals: Current signals
            timestamp: Current timestamp
        """
        # Fill any pending orders
        self._fill_pending_orders(bar_data, timestamp)
        
        # Let strategy process the bar
        new_orders = self.strategy.on_bar(bar_data, bar_index, signals)
        
        # Process new orders
        for order in new_orders:
            self._process_order(order, bar_data, timestamp)
        
        # Update portfolio
        open_positions = self.strategy.get_open_positions()
        self.portfolio.update(timestamp, open_positions)
    
    def _process_order(self, order: Order, bar_data: pd.Series, timestamp: datetime) -> None:
        """
        Process a new order.
        
        Args:
            order: Order to process
            bar_data: Current bar data
            timestamp: Current timestamp
        """
        if order.order_type == OrderType.MARKET:
            # Execute market order immediately
            fill_price = self._calculate_fill_price(order, bar_data)
            self._fill_order(order, fill_price, timestamp)
        else:
            # Add to pending orders
            self.pending_orders[order.order_id] = order
            logger.debug(f"Added pending order: {order.order_id}")
    
    def _fill_pending_orders(self, bar_data: pd.Series, timestamp: datetime) -> None:
        """Fill any pending orders that can be executed."""
        orders_to_fill = []
        
        for order_id, order in self.pending_orders.items():
            if self._can_fill_order(order, bar_data):
                fill_price = self._calculate_fill_price(order, bar_data)
                orders_to_fill.append((order, fill_price))
        
        # Fill orders
        for order, fill_price in orders_to_fill:
            self._fill_order(order, fill_price, timestamp)
            del self.pending_orders[order.order_id]
    
    def _can_fill_order(self, order: Order, bar_data: pd.Series) -> bool:
        """Check if an order can be filled."""
        if order.order_type == OrderType.MARKET:
            return True
        elif order.order_type == OrderType.LIMIT:
            if order.side.value == 'buy':
                return bar_data['Low'] <= order.price
            else:
                return bar_data['High'] >= order.price
        elif order.order_type == OrderType.STOP:
            if order.side.value == 'buy':
                return bar_data['High'] >= order.price
            else:
                return bar_data['Low'] <= order.price
        
        return False
    
    def _calculate_fill_price(self, order: Order, bar_data: pd.Series) -> float:
        """
        Calculate the fill price for an order.
        
        Args:
            order: Order to fill
            bar_data: Current bar data
            
        Returns:
            Fill price including spread and slippage
        """
        if order.order_type == OrderType.MARKET:
            base_price = bar_data['Close']
        elif order.order_type == OrderType.LIMIT:
            base_price = order.price
        elif order.order_type == OrderType.STOP:
            base_price = order.price
        else:
            base_price = bar_data['Close']
        
        # Apply spread
        if order.side.value == 'buy':
            # Buy at ask price (add spread)
            fill_price = base_price + self.spread / 2
        else:
            # Sell at bid price (subtract spread)
            fill_price = base_price - self.spread / 2
        
        # Apply slippage
        slippage_direction = 1 if order.side.value == 'buy' else -1
        fill_price += slippage_direction * self.slippage
        
        return fill_price
    
    def _fill_order(self, order: Order, fill_price: float, timestamp: datetime) -> None:
        """
        Fill an order.
        
        Args:
            order: Order to fill
            fill_price: Fill price
            timestamp: Fill timestamp
        """
        # Calculate commission
        commission_cost = order.size * fill_price * self.commission
        
        # Fill the order
        order.fill(fill_price, timestamp)
        
        # Notify strategy
        if hasattr(self.strategy, 'fill_order'):
            self.strategy.fill_order(order.order_id, fill_price, timestamp)
        
        # Log execution
        execution_record = {
            'timestamp': timestamp,
            'order_id': order.order_id,
            'symbol': order.symbol,
            'side': order.side.value,
            'size': order.size,
            'fill_price': fill_price,
            'commission': commission_cost,
            'martingale_level': order.martingale_level
        }
        
        self.execution_log.append(execution_record)
        
        # Deduct commission from portfolio
        self.portfolio.current_balance -= commission_cost
        
        logger.debug(f"Filled order {order.order_id}: {order.side.value} {order.size} "
                    f"at {fill_price:.5f} (commission: {commission_cost:.2f})")
    
    def _close_all_positions(self, exit_price: float, timestamp: datetime) -> None:
        """Close all remaining open positions."""
        open_positions = self.strategy.get_open_positions()
        
        for position_id in list(open_positions.keys()):
            if hasattr(self.strategy, 'close_position'):
                self.strategy.close_position(position_id, exit_price, timestamp, "backtest_end")
            
            logger.info(f"Closed position {position_id} at backtest end")
    
    def _log_summary(self, results: BacktestResults) -> None:
        """Log backtest summary."""
        summary = results.performance_summary
        
        logger.info("=== BACKTEST SUMMARY ===")
        logger.info(f"Initial Balance: ${summary['initial_balance']:,.2f}")
        logger.info(f"Final Equity: ${summary['final_equity']:,.2f}")
        logger.info(f"Total Return: {summary['total_return_percent']:.2f}%")
        logger.info(f"Max Drawdown: {summary['max_drawdown_percent']:.2f}%")
        logger.info(f"Sharpe Ratio: {summary['sharpe_ratio']:.2f}")
        logger.info(f"Total Trades: {summary['total_trades']}")
        logger.info(f"Win Rate: {summary['win_rate_percent']:.1f}%")
        logger.info(f"Profit Factor: {summary['profit_factor']:.2f}")
        logger.info("========================")
    
    def get_execution_log(self) -> List[Dict[str, Any]]:
        """Get order execution log."""
        return self.execution_log.copy()
