"""
Trading parameters and default configuration for Forex backtesting system.
"""

from typing import Dict, Any

# Default trading configuration
DEFAULT_CONFIG: Dict[str, Any] = {
    'trading': {
        'symbol': 'AUDNZD',
        'timeframe': '5m',
        'initial_balance': 10000.0,
        'risk_per_trade': 0.02,
        'min_profit_pips': 5,
    },
    'strategy': {
        'name': 'EMA_Cross',
        'ema_fast': 55,
        'ema_slow': 89,
    },
    'risk_management': {
        'max_martingale_orders': 5,
        'atr_period': 14,
        'atr_multiplier_target': 2.0,
        'atr_multiplier_martingale': 1.5,
        'max_drawdown_percent': 20.0,
        'position_size_multiplier': 1.5,
    },
    'data': {
        'source': 'yfinance',
        'start_date': '2023-01-01',
        'end_date': '2024-01-01',
        'data_path': 'data/',
        'local_data_path': 'data/local/',
        'local_file_pattern': '{symbol}_{timeframe}.csv',
        'fallback_to_online': True,
        'datetime_column': 'Date',
        'timezone': 'UTC',
    },
    'backtesting': {
        'commission': 0.0002,
        'spread': 0.00015,
        'slippage': 0.00005,
    },
    'visualization': {
        'chart_width': 1200,
        'chart_height': 800,
        'show_volume': False,
        'show_indicators': True,
        'export_format': 'html',
    },
    'reporting': {
        'output_dir': 'results/',
        'generate_pdf': True,
        'generate_csv': True,
        'include_trade_log': True,
    },
    'logging': {
        'level': 'INFO',
        'file': 'logs/backtest.log',
        'console': True,
    }
}

# Legacy alias for backward compatibility
TRADING_CONFIG = DEFAULT_CONFIG

# Supported symbols and their pip values
SYMBOL_INFO = {
    'AUDUSD': {'pip_value': 0.0001, 'digits': 5},
    'AUDNZD': {'pip_value': 0.0001, 'digits': 5},
    'EURUSD': {'pip_value': 0.0001, 'digits': 5},
    'GBPUSD': {'pip_value': 0.0001, 'digits': 5},
    'USDJPY': {'pip_value': 0.01, 'digits': 3},
    'USDCHF': {'pip_value': 0.0001, 'digits': 5},
    'USDCAD': {'pip_value': 0.0001, 'digits': 5},
    'NZDUSD': {'pip_value': 0.0001, 'digits': 5},
}

# Supported timeframes
SUPPORTED_TIMEFRAMES = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']

# Local file naming patterns
LOCAL_FILE_PATTERNS = {
    'standard': '{symbol}_{timeframe}.csv',
    'mt5': '{symbol}{timeframe}.csv',
    'tradingview': '{symbol}_{timeframe}_TradingView.csv',
    'custom': '{symbol}_{timeframe}_{source}.csv'
}

# Common datetime column names
DATETIME_COLUMNS = ['Date', 'Datetime', 'Time', 'Timestamp', 'date', 'datetime', 'time']

# Required OHLC columns (case-insensitive matching)
REQUIRED_OHLC_COLUMNS = ['Open', 'High', 'Low', 'Close']
OPTIONAL_COLUMNS = ['Volume', 'Adj Close', 'Tick Volume']

# Data source configurations
DATA_SOURCES = {
    'yfinance': {
        'module': 'yfinance',
        'requires_api_key': False,
        'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '1d'],
        'type': 'online'
    },
    'alphavantage': {
        'module': 'alpha_vantage',
        'requires_api_key': True,
        'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '1d'],
        'type': 'online'
    },
    'mt5': {
        'module': 'MetaTrader5',
        'requires_api_key': False,
        'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'type': 'online'
    },
    'local': {
        'module': 'local_csv',
        'requires_api_key': False,
        'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'type': 'local'
    }
}

# Configuration validation rules
CONFIG_VALIDATION_RULES = {
    'trading.symbol': {
        'type': str,
        'required': True,
        'choices': list(SYMBOL_INFO.keys()),
    },
    'trading.timeframe': {
        'type': str,
        'required': True,
        'choices': SUPPORTED_TIMEFRAMES,
    },
    'trading.initial_balance': {
        'type': (int, float),
        'required': True,
        'min_value': 1000,
    },
    'trading.risk_per_trade': {
        'type': float,
        'required': True,
        'min_value': 0.001,
        'max_value': 0.1,
    },
    'strategy.ema_fast': {
        'type': int,
        'required': True,
        'min_value': 5,
        'max_value': 200,
    },
    'strategy.ema_slow': {
        'type': int,
        'required': True,
        'min_value': 10,
        'max_value': 500,
    },
    'risk_management.max_martingale_orders': {
        'type': int,
        'required': True,
        'min_value': 1,
        'max_value': 10,
    },
    'risk_management.atr_period': {
        'type': int,
        'required': True,
        'min_value': 5,
        'max_value': 50,
    },
    'data.source': {
        'type': str,
        'required': True,
        'choices': list(DATA_SOURCES.keys()),
    },
    'data.local_data_path': {
        'type': str,
        'required': False,
    },
    'data.local_file_pattern': {
        'type': str,
        'required': False,
    },
    'data.fallback_to_online': {
        'type': bool,
        'required': False,
    },
}
