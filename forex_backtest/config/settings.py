"""
Configuration management for Forex backtesting system.
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path

from .trading_params import DEFAULT_CONFIG, CONFIG_VALIDATION_RULES


logger = logging.getLogger(__name__)


class ConfigurationError(Exception):
    """Custom exception for configuration errors."""
    pass


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from YAML or JSON file.
    
    Args:
        config_path: Path to configuration file. If None, uses default.
        
    Returns:
        Dictionary containing configuration parameters.
        
    Raises:
        ConfigurationError: If configuration file cannot be loaded or is invalid.
    """
    if config_path is None:
        config_path = "config/settings.yaml"
    
    config_path = Path(config_path)
    
    if not config_path.exists():
        logger.warning(f"Configuration file {config_path} not found. Using default configuration.")
        return DEFAULT_CONFIG.copy()
    
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                config = yaml.safe_load(file)
            elif config_path.suffix.lower() == '.json':
                config = json.load(file)
            else:
                raise ConfigurationError(f"Unsupported configuration file format: {config_path.suffix}")
        
        # Merge with defaults
        merged_config = _merge_configs(DEFAULT_CONFIG, config)
        
        # Validate configuration
        validate_config(merged_config)
        
        logger.info(f"Configuration loaded successfully from {config_path}")
        return merged_config
        
    except yaml.YAMLError as e:
        raise ConfigurationError(f"Error parsing YAML configuration: {e}")
    except json.JSONDecodeError as e:
        raise ConfigurationError(f"Error parsing JSON configuration: {e}")
    except Exception as e:
        raise ConfigurationError(f"Error loading configuration: {e}")


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    Save configuration to YAML or JSON file.
    
    Args:
        config: Configuration dictionary to save.
        config_path: Path where to save the configuration.
        
    Raises:
        ConfigurationError: If configuration cannot be saved.
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as file:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(config, file, default_flow_style=False, indent=2)
            elif config_path.suffix.lower() == '.json':
                json.dump(config, file, indent=2)
            else:
                raise ConfigurationError(f"Unsupported configuration file format: {config_path.suffix}")
        
        logger.info(f"Configuration saved to {config_path}")
        
    except Exception as e:
        raise ConfigurationError(f"Error saving configuration: {e}")


def validate_config(config: Dict[str, Any]) -> None:
    """
    Validate configuration against predefined rules.
    
    Args:
        config: Configuration dictionary to validate.
        
    Raises:
        ConfigurationError: If configuration is invalid.
    """
    errors = []
    
    for key_path, rules in CONFIG_VALIDATION_RULES.items():
        try:
            value = _get_nested_value(config, key_path)
            
            # Check if required
            if rules.get('required', False) and value is None:
                errors.append(f"Required parameter '{key_path}' is missing")
                continue
            
            if value is None:
                continue
            
            # Check type
            expected_type = rules.get('type')
            if expected_type and not isinstance(value, expected_type):
                errors.append(f"Parameter '{key_path}' must be of type {expected_type.__name__}, got {type(value).__name__}")
                continue
            
            # Check choices
            choices = rules.get('choices')
            if choices and value not in choices:
                errors.append(f"Parameter '{key_path}' must be one of {choices}, got '{value}'")
            
            # Check min/max values
            min_value = rules.get('min_value')
            if min_value is not None and value < min_value:
                errors.append(f"Parameter '{key_path}' must be >= {min_value}, got {value}")
            
            max_value = rules.get('max_value')
            if max_value is not None and value > max_value:
                errors.append(f"Parameter '{key_path}' must be <= {max_value}, got {value}")
                
        except KeyError:
            if rules.get('required', False):
                errors.append(f"Required parameter '{key_path}' is missing")
    
    # Additional validation logic
    _validate_strategy_params(config, errors)
    _validate_data_params(config, errors)
    
    if errors:
        raise ConfigurationError("Configuration validation failed:\n" + "\n".join(f"  - {error}" for error in errors))


def _merge_configs(default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge user configuration with defaults."""
    result = default.copy()
    
    for key, value in user.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _merge_configs(result[key], value)
        else:
            result[key] = value
    
    return result


def _get_nested_value(config: Dict[str, Any], key_path: str) -> Any:
    """Get value from nested dictionary using dot notation."""
    keys = key_path.split('.')
    value = config
    
    for key in keys:
        if isinstance(value, dict) and key in value:
            value = value[key]
        else:
            return None
    
    return value


def _validate_strategy_params(config: Dict[str, Any], errors: list) -> None:
    """Validate strategy-specific parameters."""
    strategy = config.get('strategy', {})
    
    ema_fast = strategy.get('ema_fast')
    ema_slow = strategy.get('ema_slow')
    
    if ema_fast and ema_slow and ema_fast >= ema_slow:
        errors.append("Fast EMA period must be less than slow EMA period")


def _validate_data_params(config: Dict[str, Any], errors: list) -> None:
    """Validate data-specific parameters."""
    data = config.get('data', {})
    
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    
    if start_date and end_date:
        try:
            from datetime import datetime
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            
            if start >= end:
                errors.append("Start date must be before end date")
                
        except ValueError:
            errors.append("Dates must be in YYYY-MM-DD format")
