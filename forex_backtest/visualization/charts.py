"""
Interactive charting for Forex backtesting system using Plotly.
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class ChartGenerator:
    """
    Interactive chart generator for backtesting results.
    """
    
    def __init__(self, results, config: Optional[Dict[str, Any]] = None):
        """
        Initialize chart generator.
        
        Args:
            results: BacktestResults object
            config: Visualization configuration
        """
        self.results = results
        self.config = config or {}
        self.viz_config = self.config.get('visualization', {})
        
        # Chart settings
        self.chart_width = self.viz_config.get('chart_width', 1200)
        self.chart_height = self.viz_config.get('chart_height', 800)
        self.show_volume = self.viz_config.get('show_volume', False)
        self.show_indicators = self.viz_config.get('show_indicators', True)
        
        # Data
        self.market_data = results.market_data.data
        self.signals_df = getattr(results.strategy, 'signals_df', None)
        self.trade_history = results.trade_history
        self.equity_curve = results.equity_curve
        
    def create_trading_chart(self) -> go.Figure:
        """
        Create comprehensive trading chart with price, indicators, and signals.
        
        Returns:
            Plotly figure object
        """
        # Determine number of subplots
        subplot_count = 2  # Price and equity
        if self.show_volume:
            subplot_count += 1
        
        # Create subplot specifications
        subplot_specs = []
        subplot_titles = []
        row_heights = []
        
        # Main price chart
        subplot_specs.append([{"secondary_y": True}])
        subplot_titles.append("Price Chart with Indicators")
        row_heights.append(0.6)
        
        # Volume chart (if enabled)
        if self.show_volume:
            subplot_specs.append([{"secondary_y": False}])
            subplot_titles.append("Volume")
            row_heights.append(0.2)
        
        # Equity curve
        subplot_specs.append([{"secondary_y": False}])
        subplot_titles.append("Equity Curve")
        row_heights.append(0.2)
        
        # Create subplots
        fig = make_subplots(
            rows=subplot_count,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=subplot_titles,
            specs=subplot_specs,
            row_heights=row_heights
        )
        
        # Add candlestick chart
        self._add_candlestick_chart(fig, row=1)
        
        # Add indicators
        if self.show_indicators and self.signals_df is not None:
            self._add_indicators(fig, row=1)
        
        # Add trade markers
        self._add_trade_markers(fig, row=1)
        
        # Add volume (if enabled)
        current_row = 2
        if self.show_volume:
            self._add_volume_chart(fig, row=current_row)
            current_row += 1
        
        # Add equity curve
        self._add_equity_curve(fig, row=current_row)
        
        # Update layout
        self._update_chart_layout(fig)
        
        return fig
    
    def _add_candlestick_chart(self, fig: go.Figure, row: int) -> None:
        """Add candlestick chart to figure."""
        fig.add_trace(
            go.Candlestick(
                x=self.market_data.index,
                open=self.market_data['Open'],
                high=self.market_data['High'],
                low=self.market_data['Low'],
                close=self.market_data['Close'],
                name="Price",
                increasing_line_color='#00ff00',
                decreasing_line_color='#ff0000',
                increasing_fillcolor='rgba(0,255,0,0.3)',
                decreasing_fillcolor='rgba(255,0,0,0.3)'
            ),
            row=row, col=1
        )
    
    def _add_indicators(self, fig: go.Figure, row: int) -> None:
        """Add technical indicators to chart."""
        if 'Fast_EMA' in self.signals_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=self.signals_df.index,
                    y=self.signals_df['Fast_EMA'],
                    mode='lines',
                    name='EMA 55',
                    line=dict(color='blue', width=2),
                    opacity=0.8
                ),
                row=row, col=1
            )
        
        if 'Slow_EMA' in self.signals_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=self.signals_df.index,
                    y=self.signals_df['Slow_EMA'],
                    mode='lines',
                    name='EMA 89',
                    line=dict(color='orange', width=2),
                    opacity=0.8
                ),
                row=row, col=1
            )
        
        # Add ATR bands if available
        if 'ATR' in self.signals_df.columns:
            self._add_atr_bands(fig, row)
    
    def _add_atr_bands(self, fig: go.Figure, row: int) -> None:
        """Add ATR-based volatility bands."""
        if 'ATR' not in self.signals_df.columns:
            return
        
        close_prices = self.market_data['Close']
        atr_values = self.signals_df['ATR']
        
        # Calculate bands
        upper_band = close_prices + (atr_values * 2)
        lower_band = close_prices - (atr_values * 2)
        
        # Add upper band
        fig.add_trace(
            go.Scatter(
                x=self.signals_df.index,
                y=upper_band,
                mode='lines',
                name='ATR Upper',
                line=dict(color='gray', width=1, dash='dash'),
                opacity=0.5
            ),
            row=row, col=1
        )
        
        # Add lower band
        fig.add_trace(
            go.Scatter(
                x=self.signals_df.index,
                y=lower_band,
                mode='lines',
                name='ATR Lower',
                line=dict(color='gray', width=1, dash='dash'),
                opacity=0.5,
                fill='tonexty',
                fillcolor='rgba(128,128,128,0.1)'
            ),
            row=row, col=1
        )
    
    def _add_trade_markers(self, fig: go.Figure, row: int) -> None:
        """Add trade entry and exit markers."""
        if not self.trade_history:
            return
        
        # Prepare trade data
        entries = []
        exits = []
        
        for trade in self.trade_history:
            entry_time = pd.to_datetime(trade['entry_timestamp'])
            exit_time = pd.to_datetime(trade['exit_timestamp'])
            
            entries.append({
                'time': entry_time,
                'price': trade['entry_price'],
                'side': trade['side'],
                'size': trade.get('total_size', trade.get('size', 0)),
                'martingale': trade.get('martingale_orders', 0)
            })
            
            exits.append({
                'time': exit_time,
                'price': trade['exit_price'],
                'pnl': trade['pnl'],
                'side': trade['side']
            })
        
        # Add entry markers
        if entries:
            entry_df = pd.DataFrame(entries)
            
            # Long entries
            long_entries = entry_df[entry_df['side'] == 'buy']
            if not long_entries.empty:
                fig.add_trace(
                    go.Scatter(
                        x=long_entries['time'],
                        y=long_entries['price'],
                        mode='markers',
                        name='Long Entry',
                        marker=dict(
                            symbol='triangle-up',
                            size=10,
                            color='green',
                            line=dict(width=2, color='darkgreen')
                        ),
                        hovertemplate='<b>Long Entry</b><br>' +
                                    'Time: %{x}<br>' +
                                    'Price: %{y:.5f}<br>' +
                                    '<extra></extra>'
                    ),
                    row=row, col=1
                )
            
            # Short entries
            short_entries = entry_df[entry_df['side'] == 'sell']
            if not short_entries.empty:
                fig.add_trace(
                    go.Scatter(
                        x=short_entries['time'],
                        y=short_entries['price'],
                        mode='markers',
                        name='Short Entry',
                        marker=dict(
                            symbol='triangle-down',
                            size=10,
                            color='red',
                            line=dict(width=2, color='darkred')
                        ),
                        hovertemplate='<b>Short Entry</b><br>' +
                                    'Time: %{x}<br>' +
                                    'Price: %{y:.5f}<br>' +
                                    '<extra></extra>'
                    ),
                    row=row, col=1
                )
        
        # Add exit markers
        if exits:
            exit_df = pd.DataFrame(exits)
            
            # Profitable exits
            profit_exits = exit_df[exit_df['pnl'] > 0]
            if not profit_exits.empty:
                fig.add_trace(
                    go.Scatter(
                        x=profit_exits['time'],
                        y=profit_exits['price'],
                        mode='markers',
                        name='Profitable Exit',
                        marker=dict(
                            symbol='circle',
                            size=8,
                            color='lightgreen',
                            line=dict(width=2, color='green')
                        ),
                        hovertemplate='<b>Profitable Exit</b><br>' +
                                    'Time: %{x}<br>' +
                                    'Price: %{y:.5f}<br>' +
                                    'PnL: %{customdata:.2f}<br>' +
                                    '<extra></extra>',
                        customdata=profit_exits['pnl']
                    ),
                    row=row, col=1
                )
            
            # Loss exits
            loss_exits = exit_df[exit_df['pnl'] <= 0]
            if not loss_exits.empty:
                fig.add_trace(
                    go.Scatter(
                        x=loss_exits['time'],
                        y=loss_exits['price'],
                        mode='markers',
                        name='Loss Exit',
                        marker=dict(
                            symbol='circle',
                            size=8,
                            color='lightcoral',
                            line=dict(width=2, color='red')
                        ),
                        hovertemplate='<b>Loss Exit</b><br>' +
                                    'Time: %{x}<br>' +
                                    'Price: %{y:.5f}<br>' +
                                    'PnL: %{customdata:.2f}<br>' +
                                    '<extra></extra>',
                        customdata=loss_exits['pnl']
                    ),
                    row=row, col=1
                )
    
    def _add_volume_chart(self, fig: go.Figure, row: int) -> None:
        """Add volume chart."""
        if 'Volume' not in self.market_data.columns:
            return
        
        fig.add_trace(
            go.Bar(
                x=self.market_data.index,
                y=self.market_data['Volume'],
                name='Volume',
                marker_color='rgba(128,128,128,0.5)'
            ),
            row=row, col=1
        )
    
    def _add_equity_curve(self, fig: go.Figure, row: int) -> None:
        """Add equity curve chart."""
        if self.equity_curve.empty:
            return
        
        fig.add_trace(
            go.Scatter(
                x=self.equity_curve.index,
                y=self.equity_curve['equity'],
                mode='lines',
                name='Equity',
                line=dict(color='blue', width=2),
                hovertemplate='<b>Equity</b><br>' +
                            'Time: %{x}<br>' +
                            'Value: $%{y:,.2f}<br>' +
                            '<extra></extra>'
            ),
            row=row, col=1
        )
        
        # Add drawdown
        if 'drawdown' in self.equity_curve.columns:
            fig.add_trace(
                go.Scatter(
                    x=self.equity_curve.index,
                    y=self.equity_curve['drawdown'],
                    mode='lines',
                    name='Drawdown %',
                    line=dict(color='red', width=1),
                    yaxis='y2',
                    hovertemplate='<b>Drawdown</b><br>' +
                                'Time: %{x}<br>' +
                                'Drawdown: %{y:.2f}%<br>' +
                                '<extra></extra>'
                ),
                row=row, col=1, secondary_y=True
            )
    
    def _update_chart_layout(self, fig: go.Figure) -> None:
        """Update chart layout and styling."""
        fig.update_layout(
            title=f"Forex Backtest Results - {self.results.market_data.symbol}",
            width=self.chart_width,
            height=self.chart_height,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            hovermode='x unified',
            template='plotly_white'
        )
        
        # Update x-axis
        fig.update_xaxes(
            title_text="Time",
            rangeslider_visible=False,
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(128,128,128,0.2)'
        )
        
        # Update y-axes
        fig.update_yaxes(
            title_text="Price",
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(128,128,128,0.2)',
            row=1, col=1
        )
    
    def create_performance_dashboard(self) -> go.Figure:
        """Create performance dashboard with multiple metrics."""
        # Create 2x2 subplot grid
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Equity Curve', 'Monthly Returns', 'Trade Distribution', 'Drawdown'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Equity curve
        if not self.equity_curve.empty:
            fig.add_trace(
                go.Scatter(
                    x=self.equity_curve.index,
                    y=self.equity_curve['equity'],
                    mode='lines',
                    name='Equity',
                    line=dict(color='blue', width=2)
                ),
                row=1, col=1
            )
        
        # Monthly returns heatmap (simplified as bar chart)
        monthly_returns = self.results.portfolio.get_monthly_returns()
        if not monthly_returns.empty:
            # Flatten monthly returns for bar chart
            monthly_data = monthly_returns.stack().reset_index()
            monthly_data.columns = ['Year', 'Month', 'Return']
            monthly_data['Date'] = pd.to_datetime(monthly_data[['Year', 'Month']].assign(day=1))
            
            fig.add_trace(
                go.Bar(
                    x=monthly_data['Date'],
                    y=monthly_data['Return'],
                    name='Monthly Returns',
                    marker_color=['green' if x > 0 else 'red' for x in monthly_data['Return']]
                ),
                row=1, col=2
            )
        
        # Trade PnL distribution
        if self.trade_history:
            pnls = [trade['pnl'] for trade in self.trade_history]
            fig.add_trace(
                go.Histogram(
                    x=pnls,
                    name='Trade PnL',
                    nbinsx=20,
                    marker_color='lightblue',
                    opacity=0.7
                ),
                row=2, col=1
            )
        
        # Drawdown chart
        if not self.equity_curve.empty and 'drawdown' in self.equity_curve.columns:
            fig.add_trace(
                go.Scatter(
                    x=self.equity_curve.index,
                    y=self.equity_curve['drawdown'],
                    mode='lines',
                    name='Drawdown',
                    line=dict(color='red', width=2),
                    fill='tonexty'
                ),
                row=2, col=2
            )
        
        fig.update_layout(
            title="Performance Dashboard",
            height=800,
            showlegend=False,
            template='plotly_white'
        )
        
        return fig
    
    def save_chart(self, fig: go.Figure, filename: str, format: str = 'html') -> None:
        """
        Save chart to file.
        
        Args:
            fig: Plotly figure
            filename: Output filename
            format: Output format ('html', 'png', 'pdf')
        """
        try:
            if format.lower() == 'html':
                fig.write_html(filename)
            elif format.lower() == 'png':
                fig.write_image(filename, format='png')
            elif format.lower() == 'pdf':
                fig.write_image(filename, format='pdf')
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Chart saved to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save chart: {e}")
            raise
