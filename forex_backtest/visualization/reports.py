"""
Report generation for Forex backtesting system.
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path
from jinja2 import Template
import base64
import io

from ..backtesting.performance import PerformanceAnalyzer
from ..utils.helpers import format_currency, format_percentage, ensure_directory

logger = logging.getLogger(__name__)


class ReportGenerator:
    """
    Comprehensive report generator for backtesting results.
    """
    
    def __init__(self, results, config: Optional[Dict[str, Any]] = None):
        """
        Initialize report generator.
        
        Args:
            results: BacktestResults object
            config: Configuration dictionary
        """
        self.results = results
        self.config = config or {}
        self.reporting_config = self.config.get('reporting', {})
        
        # Output settings
        self.output_dir = Path(self.reporting_config.get('output_dir', 'results/'))
        self.generate_pdf = self.reporting_config.get('generate_pdf', True)
        self.generate_csv = self.reporting_config.get('generate_csv', True)
        self.include_trade_log = self.reporting_config.get('include_trade_log', True)
        
        # Ensure output directory exists
        ensure_directory(self.output_dir)
        
        # Initialize performance analyzer
        self.performance_analyzer = PerformanceAnalyzer(results)
        
    def generate_comprehensive_report(self) -> Dict[str, str]:
        """
        Generate all report formats.
        
        Returns:
            Dictionary with generated file paths
        """
        generated_files = {}
        
        try:
            # Generate HTML report
            html_file = self._generate_html_report()
            generated_files['html'] = str(html_file)
            
            # Generate CSV files
            if self.generate_csv:
                csv_files = self._generate_csv_reports()
                generated_files.update(csv_files)
            
            # Generate JSON summary
            json_file = self._generate_json_summary()
            generated_files['json'] = str(json_file)
            
            # Generate PDF report (if requested and possible)
            if self.generate_pdf:
                try:
                    pdf_file = self._generate_pdf_report(html_file)
                    generated_files['pdf'] = str(pdf_file)
                except Exception as e:
                    logger.warning(f"PDF generation failed: {e}")
            
            logger.info(f"Reports generated successfully in {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise
        
        return generated_files
    
    def _generate_html_report(self) -> Path:
        """Generate HTML report."""
        # Get comprehensive metrics
        metrics = self.performance_analyzer.calculate_advanced_metrics()
        
        # Prepare template data
        template_data = {
            'title': f'Forex Backtest Report - {self.results.market_data.symbol}',
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'config': self.config,
            'metrics': metrics,
            'trade_history': self.results.trade_history,
            'symbol': self.results.market_data.symbol,
            'timeframe': self.results.market_data.timeframe,
            'start_date': self.results.market_data.data.index[0].strftime('%Y-%m-%d'),
            'end_date': self.results.market_data.data.index[-1].strftime('%Y-%m-%d'),
        }
        
        # Generate HTML content
        html_content = self._create_html_template().render(**template_data)
        
        # Save HTML file
        html_file = self.output_dir / 'backtest_report.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report saved to {html_file}")
        return html_file
    
    def _generate_csv_reports(self) -> Dict[str, str]:
        """Generate CSV reports."""
        csv_files = {}
        
        # Trade history CSV
        if self.include_trade_log and self.results.trade_history:
            trade_df = pd.DataFrame(self.results.trade_history)
            trade_file = self.output_dir / 'trade_history.csv'
            trade_df.to_csv(trade_file, index=False)
            csv_files['trade_history'] = str(trade_file)
            logger.info(f"Trade history saved to {trade_file}")
        
        # Equity curve CSV
        if not self.results.equity_curve.empty:
            equity_file = self.output_dir / 'equity_curve.csv'
            self.results.equity_curve.to_csv(equity_file)
            csv_files['equity_curve'] = str(equity_file)
            logger.info(f"Equity curve saved to {equity_file}")
        
        # Monthly returns CSV
        monthly_returns = self.results.monthly_returns
        if not monthly_returns.empty:
            monthly_file = self.output_dir / 'monthly_returns.csv'
            monthly_returns.to_csv(monthly_file)
            csv_files['monthly_returns'] = str(monthly_file)
            logger.info(f"Monthly returns saved to {monthly_file}")
        
        return csv_files
    
    def _generate_json_summary(self) -> Path:
        """Generate JSON summary."""
        # Get comprehensive metrics
        metrics = self.performance_analyzer.calculate_advanced_metrics()
        
        # Prepare summary data
        summary = {
            'backtest_info': {
                'symbol': self.results.market_data.symbol,
                'timeframe': self.results.market_data.timeframe,
                'start_date': self.results.market_data.data.index[0].isoformat(),
                'end_date': self.results.market_data.data.index[-1].isoformat(),
                'total_bars': len(self.results.market_data.data),
                'generated_at': datetime.now().isoformat()
            },
            'performance_metrics': self._serialize_metrics(metrics),
            'configuration': self.config,
            'trade_summary': {
                'total_trades': len(self.results.trade_history),
                'first_trade': self.results.trade_history[0]['entry_timestamp'] if self.results.trade_history else None,
                'last_trade': self.results.trade_history[-1]['exit_timestamp'] if self.results.trade_history else None
            }
        }
        
        # Save JSON file
        json_file = self.output_dir / 'backtest_summary.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"JSON summary saved to {json_file}")
        return json_file
    
    def _generate_pdf_report(self, html_file: Path) -> Path:
        """Generate PDF report from HTML."""
        try:
            import weasyprint
            
            pdf_file = self.output_dir / 'backtest_report.pdf'
            
            # Convert HTML to PDF
            weasyprint.HTML(filename=str(html_file)).write_pdf(str(pdf_file))
            
            logger.info(f"PDF report saved to {pdf_file}")
            return pdf_file
            
        except ImportError:
            logger.warning("weasyprint not available, skipping PDF generation")
            raise
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            raise
    
    def _create_html_template(self) -> Template:
        """Create HTML template for report."""
        template_str = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .metric-card { background-color: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f4f4f4; }
        .trade-table { max-height: 400px; overflow-y: auto; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p><strong>Symbol:</strong> {{ symbol }} | <strong>Timeframe:</strong> {{ timeframe }}</p>
        <p><strong>Period:</strong> {{ start_date }} to {{ end_date }}</p>
        <p><strong>Generated:</strong> {{ generated_at }}</p>
    </div>

    <div class="section">
        <h2>Performance Summary</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Total Return</div>
                <div class="metric-value {{ 'positive' if metrics.total_return_percent > 0 else 'negative' }}">
                    {{ "%.2f"|format(metrics.total_return_percent) }}%
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Max Drawdown</div>
                <div class="metric-value negative">{{ "%.2f"|format(metrics.max_drawdown_percent) }}%</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Sharpe Ratio</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.sharpe_ratio) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Win Rate</div>
                <div class="metric-value">{{ "%.1f"|format(metrics.win_rate_percent) }}%</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Trades</div>
                <div class="metric-value">{{ metrics.total_trades }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Profit Factor</div>
                <div class="metric-value">{{ "%.2f"|format(metrics.profit_factor) }}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Detailed Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Initial Balance</td><td>${{ "{:,.2f}"|format(metrics.initial_balance) }}</td></tr>
            <tr><td>Final Equity</td><td>${{ "{:,.2f}"|format(metrics.final_equity) }}</td></tr>
            <tr><td>Total PnL</td><td>${{ "{:,.2f}"|format(metrics.total_realized_pnl) }}</td></tr>
            <tr><td>Annualized Return</td><td>{{ "%.2f"|format(metrics.annualized_return_percent) }}%</td></tr>
            <tr><td>Calmar Ratio</td><td>{{ "%.2f"|format(metrics.calmar_ratio) }}</td></tr>
            <tr><td>Average Trade</td><td>${{ "{:.2f}"|format(metrics.average_trade.average) }}</td></tr>
            <tr><td>Average Win</td><td>${{ "{:.2f}"|format(metrics.average_trade.average_win) }}</td></tr>
            <tr><td>Average Loss</td><td>${{ "{:.2f}"|format(metrics.average_trade.average_loss) }}</td></tr>
        </table>
    </div>

    {% if trade_history %}
    <div class="section">
        <h2>Trade History (Last 20 Trades)</h2>
        <div class="trade-table">
            <table>
                <thead>
                    <tr>
                        <th>Entry Time</th>
                        <th>Exit Time</th>
                        <th>Side</th>
                        <th>Size</th>
                        <th>Entry Price</th>
                        <th>Exit Price</th>
                        <th>PnL</th>
                        <th>Martingale</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trade_history[-20:] %}
                    <tr>
                        <td>{{ trade.entry_timestamp }}</td>
                        <td>{{ trade.exit_timestamp }}</td>
                        <td>{{ trade.side.upper() }}</td>
                        <td>{{ "%.2f"|format(trade.get('total_size', trade.get('size', 0))) }}</td>
                        <td>{{ "%.5f"|format(trade.entry_price) }}</td>
                        <td>{{ "%.5f"|format(trade.exit_price) }}</td>
                        <td class="{{ 'positive' if trade.pnl > 0 else 'negative' }}">
                            ${{ "{:.2f}"|format(trade.pnl) }}
                        </td>
                        <td>{{ trade.get('martingale_orders', 0) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>Report generated by Forex Backtesting System</p>
        <p>This report is for educational and research purposes only.</p>
    </div>
</body>
</html>
        """
        
        return Template(template_str)
    
    def _serialize_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize metrics for JSON output."""
        serialized = {}
        
        for key, value in metrics.items():
            if isinstance(value, (np.integer, np.floating)):
                serialized[key] = float(value)
            elif isinstance(value, np.ndarray):
                serialized[key] = value.tolist()
            elif isinstance(value, pd.Timestamp):
                serialized[key] = value.isoformat()
            elif isinstance(value, pd.Timedelta):
                serialized[key] = str(value)
            elif isinstance(value, dict):
                serialized[key] = self._serialize_metrics(value)
            else:
                serialized[key] = value
        
        return serialized
    
    def generate_quick_summary(self) -> str:
        """Generate a quick text summary."""
        metrics = self.performance_analyzer.calculate_advanced_metrics()
        
        summary = f"""
=== FOREX BACKTEST SUMMARY ===
Symbol: {self.results.market_data.symbol}
Period: {self.results.market_data.data.index[0].strftime('%Y-%m-%d')} to {self.results.market_data.data.index[-1].strftime('%Y-%m-%d')}

PERFORMANCE:
Total Return: {metrics['total_return_percent']:.2f}%
Max Drawdown: {metrics['max_drawdown_percent']:.2f}%
Sharpe Ratio: {metrics['sharpe_ratio']:.2f}
Win Rate: {metrics['win_rate_percent']:.1f}%

TRADING:
Total Trades: {metrics['total_trades']}
Profit Factor: {metrics['profit_factor']:.2f}
Average Trade: ${metrics['average_trade']['average']:.2f}

BALANCE:
Initial: ${metrics['initial_balance']:,.2f}
Final: ${metrics['final_equity']:,.2f}
Total PnL: ${metrics['total_realized_pnl']:,.2f}
=============================
        """
        
        return summary.strip()
