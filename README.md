# Forex Backtesting System

A comprehensive, modular Python backtesting system for Forex trading with EMA crossover strategies and martingale position management.

## Features

- **EMA Crossover Strategy**: 55/89 EMA crossover signals on M5 timeframe
- **Martingale Position Management**: Dynamic position sizing with ATR-based targets
- **No Stop Loss System**: Positions remain open until profitable
- **Interactive Visualizations**: Plotly-based charts with full interactivity
- **Comprehensive Analytics**: Detailed performance metrics and reporting
- **Modular Architecture**: Easy to extend and customize

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. (Optional) Install TA-Lib for advanced technical analysis:
```bash
# On Windows with conda:
conda install -c conda-forge ta-lib

# On macOS:
brew install ta-lib
pip install ta-lib

# On Ubuntu/Debian:
sudo apt-get install libta-lib-dev
pip install ta-lib
```

## Quick Start

1. Configure your settings in `config/settings.yaml`
2. Run the backtest:
```bash
python main.py
```

3. View results in the `results/` directory

## Configuration

Edit `config/settings.yaml` to customize:
- Trading symbol and timeframe
- Strategy parameters (EMA periods, ATR settings)
- Risk management rules
- Data source and date range
- Visualization and reporting options

## Project Structure

```
forex_backtest/
├── config/          # Configuration management
├── data/            # Data loading and preprocessing
├── indicators/      # Technical indicators (EMA, ATR)
├── strategy/        # Trading strategies and position management
├── backtesting/     # Backtesting engine and portfolio management
├── visualization/   # Charts and reports
└── utils/          # Utility functions and validators
```

## Strategy Details

### Signal Generation
- **Long Signal**: EMA 55 crosses above EMA 89
- **Short Signal**: EMA 55 crosses below EMA 89

### Position Management
- No stop losses - positions held until profitable
- ATR-based profit targets (configurable multiplier)
- Martingale system adds positions when price moves against initial entry
- Maximum 5 martingale levels (configurable)

### Risk Management
- 2% risk per trade (configurable)
- Maximum drawdown protection
- Position sizing increases with martingale levels

## Output Files

- `results/backtest_chart.html` - Interactive trading chart
- `results/performance_report.pdf` - Comprehensive performance analysis
- `results/trade_log.csv` - Detailed trade history
- `results/summary.json` - Key metrics summary

## License

This project is for educational and research purposes.
