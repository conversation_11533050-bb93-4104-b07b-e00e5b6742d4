# Forex Backtesting System

A comprehensive, modular Python backtesting system for Forex trading with EMA crossover strategies and martingale position management.

![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![License](https://img.shields.io/badge/license-Educational-green.svg)
![Status](https://img.shields.io/badge/status-Production-brightgreen.svg)

## 🚀 Features

### Strategy & Signals
- **EMA Crossover Strategy**: Configurable EMA periods (default 55/89) with anti-whipsaw filtering
- **Signal Validation**: Confirmation bars and minimum separation requirements
- **ATR-Based Analysis**: Dynamic volatility-based position sizing and targets

### Position Management
- **Martingale System**: Intelligent position averaging with configurable levels (1-10)
- **No Stop Loss**: Hold positions until profitable with ATR-based profit targets
- **Dynamic Sizing**: Position sizes scale based on account balance and volatility

### Risk Management
- **Configurable Risk**: 0.1% to 10% risk per trade
- **Drawdown Protection**: Maximum drawdown limits and circuit breakers
- **Exposure Monitoring**: Real-time margin and correlation tracking

### Visualization & Reporting
- **Interactive Charts**: Plotly-based candlestick charts with indicators and signals
- **Comprehensive Reports**: HTML, PDF, and CSV export capabilities
- **Performance Analytics**: 25+ metrics including Sharpe ratio, VaR, and drawdown analysis
- **Real-time Monitoring**: Equity curves and performance dashboards

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- 2GB RAM (4GB recommended)
- Internet connection for data download

### Quick Install
```bash
# Clone the repository
git clone <repository-url>
cd forex-backtest

# Install dependencies
pip install -r requirements.txt

# Run your first backtest
python main.py
```

### Optional Dependencies
```bash
# For advanced technical analysis
pip install ta-lib

# For PDF report generation
pip install weasyprint

# For development
pip install pytest black flake8 mypy
```

## 🎯 Quick Start

### 1. Basic Usage
```bash
# Run with default settings (AUDUSD, 2023 data)
python main.py

# Custom symbol and date range
python main.py --symbol EURUSD --start 2023-01-01 --end 2023-12-31

# Different account balance
python main.py --balance 25000
```

### 2. Configuration Files
```bash
# Use custom configuration
python main.py -c examples/conservative_config.yaml

# Available examples:
# - examples/conservative_config.yaml (low risk)
# - examples/aggressive_config.yaml (high risk)
# - examples/custom_config.yaml (balanced)
```

### 3. View Results
Results are automatically saved to `results/` directory:
- **trading_chart.html** - Interactive price chart with signals
- **performance_dashboard.html** - Comprehensive metrics dashboard
- **backtest_report.html** - Detailed HTML report
- **trade_history.csv** - Complete trade log
- **backtest_summary.json** - Key metrics in JSON format

## ⚙️ Configuration

### Key Parameters
```yaml
trading:
  symbol: "AUDUSD"              # Currency pair
  timeframe: "5m"               # 1m, 5m, 15m, 30m, 1h, 4h, 1d
  initial_balance: 10000.0      # Starting balance
  risk_per_trade: 0.02          # 2% risk per trade

strategy:
  ema_fast: 55                  # Fast EMA period
  ema_slow: 89                  # Slow EMA period

risk_management:
  max_martingale_orders: 5      # Maximum martingale levels
  atr_multiplier_target: 2.0    # Profit target multiplier
  position_size_multiplier: 1.5 # Martingale size increase
```

### Supported Symbols
- **Major Pairs**: AUDUSD, EURUSD, GBPUSD, USDCAD, USDCHF, USDJPY, NZDUSD
- **Timeframes**: 1m, 5m, 15m, 30m, 1h, 4h, 1d
- **Data Sources**: Yahoo Finance (default), Alpha Vantage, MetaTrader 5

## 📊 Strategy Details

### EMA Crossover System
- **Entry Signals**: Fast EMA crosses above/below Slow EMA
- **Signal Filtering**: Anti-whipsaw protection and confirmation requirements
- **Trend Following**: Designed for trending market conditions

### Martingale Position Management
- **No Stop Losses**: Positions held until profitable
- **Position Averaging**: Add positions when price moves against initial entry
- **ATR-Based Spacing**: Dynamic spacing based on market volatility
- **Profit Targets**: ATR-multiplied targets for consistent exits

### Risk Controls
- **Maximum Levels**: Configurable martingale limits (1-10)
- **Drawdown Limits**: Automatic trading halt at maximum drawdown
- **Position Sizing**: Dynamic sizing based on account balance and volatility

## 📈 Performance Metrics

### Core Metrics
- **Returns**: Total, annualized, and risk-adjusted returns
- **Risk Measures**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Drawdown**: Maximum drawdown, recovery factor, drawdown duration
- **Trade Analysis**: Win rate, profit factor, average trade statistics

### Advanced Analytics
- **Value at Risk**: 95% and 99% confidence levels
- **Volatility**: Annualized volatility and downside deviation
- **Time Analysis**: Monthly returns, best/worst periods
- **Martingale Stats**: Usage frequency and performance by level

## 🏗️ Project Structure

```
forex_backtest/
├── config/                 # Configuration management
│   ├── settings.py        # Config loading and validation
│   └── trading_params.py  # Default parameters and validation rules
├── data/                  # Data handling
│   ├── data_loader.py     # Multi-source data loading
│   └── market_data.py     # Data preprocessing and validation
├── indicators/            # Technical indicators
│   ├── ema.py            # EMA calculations
│   ├── volatility.py     # ATR and volatility measures
│   └── signals.py        # Signal generation and filtering
├── strategy/              # Trading strategy
│   ├── base_strategy.py   # Abstract strategy framework
│   ├── ema_cross_strategy.py # EMA crossover implementation
│   └── position_manager.py   # Martingale position management
├── backtesting/           # Backtesting engine
│   ├── backtest_engine.py # Event-driven backtesting
│   ├── portfolio.py       # Portfolio management
│   └── performance.py     # Performance analysis
├── visualization/         # Charts and reports
│   ├── charts.py         # Interactive Plotly charts
│   └── reports.py        # HTML/PDF report generation
└── utils/                # Utilities
    ├── helpers.py        # Helper functions
    └── validators.py     # Input validation
```

## 🧪 Testing

```bash
# Run all tests
python run_tests.py

# Run specific test module
python run_tests.py test_indicators

# Run with coverage
pytest --cov=forex_backtest tests/
```

## 📚 Documentation

- **[User Guide](docs/USER_GUIDE.md)** - Complete installation and usage instructions
- **[Strategy Guide](docs/STRATEGY_GUIDE.md)** - Detailed strategy explanation and optimization
- **[Examples](examples/)** - Sample configurations and usage examples
- **[Changelog](CHANGELOG.md)** - Version history and updates

## ⚠️ Risk Warning

**Important**: This system implements a martingale strategy which can result in significant losses. Key risks include:

- **Exponential Position Growth**: Position sizes increase rapidly with each martingale level
- **No Stop Losses**: Unlimited loss potential per trade sequence
- **Market Gaps**: Overnight gaps can cause large unexpected losses
- **Drawdown Risk**: Extended losing periods can deplete account balance

**Recommendations**:
- Start with small position sizes
- Thoroughly backtest before live trading
- Never risk more than you can afford to lose
- Consider maximum drawdown limits
- Monitor correlation between positions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add unit tests for new features
- Update documentation for changes
- Test on multiple platforms

## 📄 License

This project is for **educational and research purposes only**. Not intended for commercial use or live trading without proper risk management and testing.

## 🙏 Acknowledgments

- Built with Python, pandas, numpy, and plotly
- Inspired by quantitative trading research and best practices
- Thanks to the open-source community for excellent libraries

---

**Disclaimer**: Past performance does not guarantee future results. Trading forex involves substantial risk and may not be suitable for all investors.
