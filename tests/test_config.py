"""
Unit tests for configuration management.
"""

import unittest
import tempfile
import yaml
import json
from pathlib import Path

from forex_backtest.config.settings import load_config, save_config, validate_config
from forex_backtest.config.trading_params import DEFAULT_CONFIG


class TestConfigManagement(unittest.TestCase):
    """Test configuration loading and validation."""
    
    def setUp(self):
        """Set up test configuration."""
        self.valid_config = {
            'trading': {
                'symbol': 'AUDUSD',
                'timeframe': '5m',
                'initial_balance': 10000.0,
                'risk_per_trade': 0.02
            },
            'strategy': {
                'ema_fast': 55,
                'ema_slow': 89
            },
            'risk_management': {
                'max_martingale_orders': 5,
                'atr_period': 14,
                'atr_multiplier_target': 2.0
            },
            'data': {
                'source': 'yfinance',
                'start_date': '2023-01-01',
                'end_date': '2023-12-31'
            }
        }
    
    def test_load_default_config(self):
        """Test loading default configuration."""
        # Test with non-existent file (should return defaults)
        config = load_config('non_existent_file.yaml')
        
        self.assertIsInstance(config, dict)
        self.assertIn('trading', config)
        self.assertIn('strategy', config)
    
    def test_load_yaml_config(self):
        """Test loading YAML configuration."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.valid_config, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            
            self.assertEqual(config['trading']['symbol'], 'AUDUSD')
            self.assertEqual(config['strategy']['ema_fast'], 55)
            
        finally:
            Path(temp_path).unlink()
    
    def test_load_json_config(self):
        """Test loading JSON configuration."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.valid_config, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            
            self.assertEqual(config['trading']['symbol'], 'AUDUSD')
            self.assertEqual(config['strategy']['ema_fast'], 55)
            
        finally:
            Path(temp_path).unlink()
    
    def test_save_yaml_config(self):
        """Test saving YAML configuration."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            save_config(self.valid_config, temp_path)
            
            # Load it back and verify
            loaded_config = load_config(temp_path)
            self.assertEqual(loaded_config['trading']['symbol'], 'AUDUSD')
            
        finally:
            Path(temp_path).unlink()
    
    def test_config_validation_valid(self):
        """Test validation of valid configuration."""
        # Should not raise exception
        validate_config(self.valid_config)
    
    def test_config_validation_invalid_symbol(self):
        """Test validation with invalid symbol."""
        invalid_config = self.valid_config.copy()
        invalid_config['trading']['symbol'] = 'INVALID'
        
        with self.assertRaises(Exception):
            validate_config(invalid_config)
    
    def test_config_validation_invalid_timeframe(self):
        """Test validation with invalid timeframe."""
        invalid_config = self.valid_config.copy()
        invalid_config['trading']['timeframe'] = '2m'  # Not supported
        
        with self.assertRaises(Exception):
            validate_config(invalid_config)
    
    def test_config_validation_invalid_ema_periods(self):
        """Test validation with invalid EMA periods."""
        invalid_config = self.valid_config.copy()
        invalid_config['strategy']['ema_fast'] = 100  # Greater than slow
        invalid_config['strategy']['ema_slow'] = 50
        
        with self.assertRaises(Exception):
            validate_config(invalid_config)
    
    def test_config_validation_invalid_dates(self):
        """Test validation with invalid dates."""
        invalid_config = self.valid_config.copy()
        invalid_config['data']['start_date'] = '2023-12-31'
        invalid_config['data']['end_date'] = '2023-01-01'  # End before start
        
        with self.assertRaises(Exception):
            validate_config(invalid_config)
    
    def test_config_merging(self):
        """Test configuration merging with defaults."""
        partial_config = {
            'trading': {
                'symbol': 'EURUSD'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(partial_config, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            
            # Should have merged with defaults
            self.assertEqual(config['trading']['symbol'], 'EURUSD')  # Overridden
            self.assertIn('initial_balance', config['trading'])  # From defaults
            self.assertIn('strategy', config)  # From defaults
            
        finally:
            Path(temp_path).unlink()


if __name__ == '__main__':
    unittest.main()
