"""
Unit tests for AUDNZD currency pair support.
"""

import unittest
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from forex_backtest.config.trading_params import SYMBOL_INFO, DEFAULT_CONFIG
from forex_backtest.utils.validators import validate_symbol
from forex_backtest.data.data_loader import DataLoader
from forex_backtest import load_config


class TestAUDNZDSupport(unittest.TestCase):
    """Test AUDNZD currency pair support."""
    
    def test_audnzd_in_symbol_info(self):
        """Test that AUDNZD is included in SYMBOL_INFO."""
        self.assertIn('AUDNZD', SYMBOL_INFO)
        
        audnzd_info = SYMBOL_INFO['AUDNZD']
        self.assertEqual(audnzd_info['pip_value'], 0.0001)
        self.assertEqual(audnzd_info['digits'], 5)
    
    def test_default_symbol_is_audnzd(self):
        """Test that default symbol is set to AUDNZD."""
        default_symbol = DEFAULT_CONFIG['trading']['symbol']
        self.assertEqual(default_symbol, 'AUDNZD')
    
    def test_audnzd_symbol_validation(self):
        """Test AUDNZD symbol validation."""
        self.assertTrue(validate_symbol('AUDNZD'))
    
    def test_audnzd_yfinance_symbol_conversion(self):
        """Test AUDNZD symbol conversion for yfinance."""
        config = {'data': {'source': 'yfinance'}}
        data_loader = DataLoader(config)
        
        yf_symbol = data_loader._convert_symbol_for_yfinance('AUDNZD')
        self.assertEqual(yf_symbol, 'AUDNZD=X')
    
    def test_audnzd_in_supported_symbols_list(self):
        """Test that AUDNZD is in the list of supported symbols."""
        supported_symbols = list(SYMBOL_INFO.keys())
        self.assertIn('AUDNZD', supported_symbols)
        
        # Verify it's positioned correctly (after AUDUSD)
        audusd_index = supported_symbols.index('AUDUSD')
        audnzd_index = supported_symbols.index('AUDNZD')
        self.assertEqual(audnzd_index, audusd_index + 1)
    
    def test_configuration_loading_with_audnzd(self):
        """Test loading configuration files with AUDNZD."""
        # Test main configuration
        config = load_config('config/settings.yaml')
        self.assertEqual(config['trading']['symbol'], 'AUDNZD')
        
        # Test example configurations
        example_configs = [
            'examples/conservative_config.yaml',
            'examples/custom_config.yaml',
            'examples/local_data_config.yaml'
        ]
        
        for config_file in example_configs:
            if Path(config_file).exists():
                config = load_config(config_file)
                self.assertEqual(config['trading']['symbol'], 'AUDNZD',
                               f"Config file {config_file} should use AUDNZD")
    
    def test_audnzd_pip_value_consistency(self):
        """Test that AUDNZD pip value is consistent with other major pairs."""
        audnzd_pip = SYMBOL_INFO['AUDNZD']['pip_value']
        audusd_pip = SYMBOL_INFO['AUDUSD']['pip_value']
        eurusd_pip = SYMBOL_INFO['EURUSD']['pip_value']
        
        # AUDNZD should have same pip value as other major pairs (except JPY)
        self.assertEqual(audnzd_pip, audusd_pip)
        self.assertEqual(audnzd_pip, eurusd_pip)
        self.assertEqual(audnzd_pip, 0.0001)
    
    def test_audnzd_digits_consistency(self):
        """Test that AUDNZD digits are consistent with other major pairs."""
        audnzd_digits = SYMBOL_INFO['AUDNZD']['digits']
        audusd_digits = SYMBOL_INFO['AUDUSD']['digits']
        
        # AUDNZD should have same digits as AUDUSD
        self.assertEqual(audnzd_digits, audusd_digits)
        self.assertEqual(audnzd_digits, 5)


class TestAUDNZDDataLoading(unittest.TestCase):
    """Test AUDNZD data loading functionality."""
    
    def setUp(self):
        """Set up test configuration."""
        self.config = {
            'data': {
                'source': 'yfinance',
                'start_date': '2023-01-01',
                'end_date': '2023-01-05'
            }
        }
    
    def test_audnzd_online_data_loading(self):
        """Test loading AUDNZD data from online sources."""
        data_loader = DataLoader(self.config)
        
        try:
            # Test loading AUDNZD data
            market_data = data_loader.load_data('AUDNZD', '2023-01-01', '2023-01-05', '1d')
            
            # Verify data was loaded
            self.assertIsNotNone(market_data)
            self.assertEqual(market_data.symbol, 'AUDNZD')
            self.assertEqual(market_data.timeframe, '1d')
            self.assertGreater(len(market_data.data), 0)
            
            # Verify required columns exist
            required_columns = ['Open', 'High', 'Low', 'Close']
            for col in required_columns:
                self.assertIn(col, market_data.data.columns)
            
            # Verify data types
            for col in required_columns:
                self.assertTrue(market_data.data[col].dtype.kind in 'fi')  # float or int
            
        except Exception as e:
            # If online data fails, it might be due to network issues
            # This is acceptable for unit tests
            self.skipTest(f"Online data loading failed (network issue): {e}")
    
    def test_audnzd_local_data_loading(self):
        """Test loading AUDNZD data from local files."""
        local_config = {
            'data': {
                'source': 'local',
                'local_data_path': 'examples/local_data/',
                'local_file_pattern': '{symbol}_{timeframe}.csv',
                'fallback_to_online': False
            }
        }
        
        data_loader = DataLoader(local_config)
        
        # Check if local AUDNZD file exists
        local_file = Path('examples/local_data/AUDNZD_5m.csv')
        if local_file.exists():
            try:
                market_data = data_loader.load_data('AUDNZD', '2023-01-01', '2023-01-02', '5m')
                
                self.assertIsNotNone(market_data)
                self.assertEqual(market_data.symbol, 'AUDNZD')
                self.assertEqual(market_data.timeframe, '5m')
                self.assertGreater(len(market_data.data), 0)
                
            except Exception as e:
                self.fail(f"Local AUDNZD data loading failed: {e}")
        else:
            self.skipTest("Local AUDNZD data file not found")


class TestAUDNZDBacktesting(unittest.TestCase):
    """Test AUDNZD backtesting functionality."""
    
    def test_audnzd_strategy_initialization(self):
        """Test strategy initialization with AUDNZD."""
        from forex_backtest.strategy.ema_cross_strategy import EMACrossStrategy
        
        config = {
            'trading': {
                'symbol': 'AUDNZD',
                'timeframe': '5m',
                'initial_balance': 10000.0,
                'risk_per_trade': 0.02,
                'min_profit_pips': 5,
            },
            'strategy': {
                'name': 'EMA_Cross',
                'ema_fast': 55,
                'ema_slow': 89,
            },
            'risk_management': {
                'max_martingale_orders': 5,
                'atr_period': 14,
                'atr_multiplier_target': 2.0,
                'atr_multiplier_martingale': 1.5,
                'position_size_multiplier': 1.5,
            }
        }
        
        try:
            strategy = EMACrossStrategy(config)
            # Just verify strategy was created successfully
            self.assertIsNotNone(strategy)

        except Exception as e:
            self.fail(f"AUDNZD strategy initialization failed: {e}")
    
    def test_audnzd_pip_value_calculation(self):
        """Test pip value calculation for AUDNZD."""
        # Test pip value from SYMBOL_INFO
        audnzd_info = SYMBOL_INFO['AUDNZD']
        pip_value = audnzd_info['pip_value']

        # AUDNZD should have standard pip value
        self.assertEqual(pip_value, 0.0001)


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
