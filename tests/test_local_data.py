"""
Unit tests for local data loading functionality.
"""

import unittest
import pandas as pd
import numpy as np
import tempfile
import os
from pathlib import Path
from datetime import datetime, timedelta

from forex_backtest.data.local_loader import LocalDataLoader, LocalDataLoadingError
from forex_backtest.data.data_loader import DataLoader
from forex_backtest.utils.validators import validate_local_csv_file, validate_local_data_directory


class TestLocalDataLoader(unittest.TestCase):
    """Test local CSV data loading."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'data': {
                'local_data_path': self.temp_dir,
                'local_file_pattern': '{symbol}_{timeframe}.csv',
                'datetime_column': 'Date',
                'timezone': 'UTC',
                'fallback_to_online': False
            }
        }
        
        # Create sample CSV data
        self.sample_data = self._create_sample_data()
        
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample OHLC data."""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='5T')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 1.0500
        returns = np.random.randn(100) * 0.001
        prices = base_price + np.cumsum(returns)
        
        data = pd.DataFrame({
            'Date': dates,
            'Open': prices * (1 + np.random.randn(100) * 0.0001),
            'High': prices * (1 + abs(np.random.randn(100)) * 0.0002),
            'Low': prices * (1 - abs(np.random.randn(100)) * 0.0002),
            'Close': prices,
            'Volume': np.random.randint(1000, 5000, 100)
        })
        
        # Ensure OHLC relationships are valid
        data['High'] = np.maximum.reduce([data['High'], data['Open'], data['Close']])
        data['Low'] = np.minimum.reduce([data['Low'], data['Open'], data['Close']])
        
        return data
    
    def _save_csv_file(self, filename: str, data: pd.DataFrame = None) -> str:
        """Save CSV file to temp directory."""
        if data is None:
            data = self.sample_data
        
        filepath = os.path.join(self.temp_dir, filename)
        data.to_csv(filepath, index=False)
        return filepath
    
    def test_local_loader_initialization(self):
        """Test local data loader initialization."""
        loader = LocalDataLoader(self.config)
        
        self.assertEqual(str(loader.local_data_path), self.temp_dir)
        self.assertEqual(loader.file_pattern, '{symbol}_{timeframe}.csv')
        self.assertEqual(loader.datetime_column, 'Date')
    
    def test_find_csv_file_exact_match(self):
        """Test finding CSV file with exact pattern match."""
        # Create test file
        self._save_csv_file('AUDUSD_5m.csv')
        
        loader = LocalDataLoader(self.config)
        found_file = loader._find_csv_file('AUDUSD', '5m')
        
        self.assertIsNotNone(found_file)
        self.assertEqual(found_file.name, 'AUDUSD_5m.csv')
    
    def test_find_csv_file_alternative_patterns(self):
        """Test finding CSV file with alternative naming patterns."""
        # Create test file with alternative pattern
        self._save_csv_file('AUDUSD5m.csv')
        
        loader = LocalDataLoader(self.config)
        found_file = loader._find_csv_file('AUDUSD', '5m')
        
        self.assertIsNotNone(found_file)
        self.assertEqual(found_file.name, 'AUDUSD5m.csv')
    
    def test_find_csv_file_case_insensitive(self):
        """Test case-insensitive file finding."""
        # Create test file with different case
        self._save_csv_file('audusd_5m.csv')

        loader = LocalDataLoader(self.config)
        found_file = loader._find_csv_file('AUDUSD', '5m')

        self.assertIsNotNone(found_file)
        # The file should be found regardless of case
        self.assertIn('audusd', found_file.name.lower())
    
    def test_load_csv_file_valid(self):
        """Test loading valid CSV file."""
        filepath = self._save_csv_file('AUDUSD_5m.csv')
        
        loader = LocalDataLoader(self.config)
        data = loader._load_csv_file(Path(filepath))
        
        self.assertIsInstance(data, pd.DataFrame)
        self.assertGreater(len(data), 0)
        self.assertIn('Open', data.columns)
        self.assertIn('High', data.columns)
        self.assertIn('Low', data.columns)
        self.assertIn('Close', data.columns)
        self.assertIsInstance(data.index, pd.DatetimeIndex)
    
    def test_load_csv_file_missing_columns(self):
        """Test loading CSV file with missing OHLC columns."""
        # Create invalid data
        invalid_data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=10, freq='5T'),
            'Price': np.random.randn(10)
        })
        
        filepath = self._save_csv_file('invalid.csv', invalid_data)
        
        loader = LocalDataLoader(self.config)
        
        with self.assertRaises(LocalDataLoadingError):
            loader._load_csv_file(Path(filepath))
    
    def test_load_data_success(self):
        """Test successful data loading."""
        self._save_csv_file('AUDUSD_5m.csv')
        
        loader = LocalDataLoader(self.config)
        market_data = loader.load_data('AUDUSD', '2023-01-01', '2023-01-02', '5m')
        
        self.assertIsNotNone(market_data)
        self.assertEqual(market_data.symbol, 'AUDUSD')
        self.assertEqual(market_data.timeframe, '5m')
        self.assertGreater(len(market_data.data), 0)
    
    def test_load_data_file_not_found(self):
        """Test data loading when file doesn't exist."""
        loader = LocalDataLoader(self.config)
        
        with self.assertRaises(LocalDataLoadingError) as context:
            loader.load_data('NONEXISTENT', '2023-01-01', '2023-01-02', '5m')
        
        self.assertIn('No local data file found', str(context.exception))
    
    def test_list_available_files(self):
        """Test listing available files."""
        self._save_csv_file('AUDUSD_5m.csv')
        self._save_csv_file('EURUSD_1h.csv')
        
        loader = LocalDataLoader(self.config)
        files = loader.list_available_files()
        
        self.assertEqual(len(files), 2)
        filenames = [f['filename'] for f in files]
        self.assertIn('AUDUSD_5m.csv', filenames)
        self.assertIn('EURUSD_1h.csv', filenames)
    
    def test_parse_filename(self):
        """Test filename parsing."""
        loader = LocalDataLoader(self.config)
        
        # Test standard pattern
        symbol, timeframe = loader._parse_filename('AUDUSD_5m.csv')
        self.assertEqual(symbol, 'AUDUSD')
        self.assertEqual(timeframe, '5m')
        
        # Test alternative pattern
        symbol, timeframe = loader._parse_filename('EURUSD1h.csv')
        self.assertEqual(symbol, 'EURUSD')
        self.assertEqual(timeframe, '1h')


class TestDataLoaderIntegration(unittest.TestCase):
    """Test integration with main DataLoader."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'data': {
                'source': 'local',
                'local_data_path': self.temp_dir,
                'local_file_pattern': '{symbol}_{timeframe}.csv',
                'fallback_to_online': False
            }
        }
        
        # Create sample data
        self._create_sample_file()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_sample_file(self):
        """Create sample CSV file."""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='5T')
        data = pd.DataFrame({
            'Date': dates,
            'Open': 1.0500 + np.random.randn(50) * 0.001,
            'High': 1.0520 + np.random.randn(50) * 0.001,
            'Low': 1.0480 + np.random.randn(50) * 0.001,
            'Close': 1.0510 + np.random.randn(50) * 0.001,
            'Volume': np.random.randint(1000, 5000, 50)
        })
        
        filepath = os.path.join(self.temp_dir, 'AUDUSD_5m.csv')
        data.to_csv(filepath, index=False)
    
    def test_data_loader_local_source(self):
        """Test DataLoader with local source."""
        loader = DataLoader(self.config)
        market_data = loader.load_data('AUDUSD', '2023-01-01', '2023-01-02', '5m')
        
        self.assertIsNotNone(market_data)
        self.assertEqual(market_data.symbol, 'AUDUSD')
        self.assertGreater(len(market_data.data), 0)


class TestLocalDataValidation(unittest.TestCase):
    """Test local data validation functions."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_validate_valid_csv_file(self):
        """Test validation of valid CSV file."""
        # Create valid CSV file
        data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=10, freq='5T'),
            'Open': np.random.randn(10) + 1.05,
            'High': np.random.randn(10) + 1.052,
            'Low': np.random.randn(10) + 1.048,
            'Close': np.random.randn(10) + 1.051,
            'Volume': np.random.randint(1000, 5000, 10)
        })
        
        filepath = os.path.join(self.temp_dir, 'valid.csv')
        data.to_csv(filepath, index=False)
        
        errors = validate_local_csv_file(filepath)
        self.assertEqual(len(errors), 0)
    
    def test_validate_invalid_csv_file(self):
        """Test validation of invalid CSV file."""
        # Create invalid CSV file (missing OHLC columns)
        data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=10, freq='5T'),
            'Price': np.random.randn(10)
        })
        
        filepath = os.path.join(self.temp_dir, 'invalid.csv')
        data.to_csv(filepath, index=False)
        
        errors = validate_local_csv_file(filepath)
        self.assertGreater(len(errors), 0)
    
    def test_validate_directory(self):
        """Test directory validation."""
        # Create valid CSV file
        data = pd.DataFrame({
            'Date': pd.date_range('2023-01-01', periods=10, freq='5T'),
            'Open': np.random.randn(10) + 1.05,
            'High': np.random.randn(10) + 1.052,
            'Low': np.random.randn(10) + 1.048,
            'Close': np.random.randn(10) + 1.051,
            'Volume': np.random.randint(1000, 5000, 10)
        })
        
        filepath = os.path.join(self.temp_dir, 'AUDUSD_5m.csv')
        data.to_csv(filepath, index=False)
        
        result = validate_local_data_directory(self.temp_dir)
        
        self.assertTrue(result['valid'])
        self.assertEqual(result['files_found'], 1)
        self.assertEqual(result['valid_files'], 1)


if __name__ == '__main__':
    unittest.main()
