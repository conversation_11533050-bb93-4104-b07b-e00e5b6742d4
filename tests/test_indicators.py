"""
Unit tests for technical indicators.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from forex_backtest.indicators.ema import calculate_ema, EMAIndicator
from forex_backtest.indicators.volatility import calculate_atr, ATRIndicator
from forex_backtest.indicators.signals import SignalGenerator, CrossoverSignal


class TestEMAIndicator(unittest.TestCase):
    """Test EMA calculations."""
    
    def setUp(self):
        """Set up test data."""
        # Create sample price data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 1.0 + np.cumsum(np.random.randn(100) * 0.01)
        
        self.price_series = pd.Series(prices, index=dates)
        self.price_df = pd.DataFrame({
            'Open': prices * 0.999,
            'High': prices * 1.002,
            'Low': prices * 0.998,
            'Close': prices
        }, index=dates)
    
    def test_ema_calculation(self):
        """Test basic EMA calculation."""
        ema = calculate_ema(self.price_series, 10)
        
        # Check that EMA is calculated
        self.assertIsInstance(ema, pd.Series)
        self.assertEqual(len(ema), len(self.price_series))
        
        # Check that initial values are NaN
        self.assertTrue(pd.isna(ema.iloc[:9]).all())
        
        # Check that EMA values are reasonable
        self.assertFalse(pd.isna(ema.iloc[9:]).any())
    
    def test_ema_with_dataframe(self):
        """Test EMA calculation with DataFrame input."""
        ema = calculate_ema(self.price_df, 10, 'Close')
        
        self.assertIsInstance(ema, pd.Series)
        self.assertEqual(len(ema), len(self.price_df))
    
    def test_ema_indicator_class(self):
        """Test EMA indicator class."""
        indicator = EMAIndicator(10)
        ema = indicator.calculate(self.price_series)
        
        self.assertIsInstance(ema, pd.Series)
        self.assertTrue(indicator.is_ready(20))
        self.assertFalse(indicator.is_ready(5))
    
    def test_invalid_period(self):
        """Test EMA with invalid period."""
        with self.assertRaises(ValueError):
            calculate_ema(self.price_series, 0)
        
        with self.assertRaises(ValueError):
            calculate_ema(self.price_series, -5)


class TestATRIndicator(unittest.TestCase):
    """Test ATR calculations."""
    
    def setUp(self):
        """Set up test data."""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        # Create realistic OHLC data
        close_prices = 1.0 + np.cumsum(np.random.randn(50) * 0.01)
        
        self.ohlc_data = pd.DataFrame({
            'Open': close_prices * (1 + np.random.randn(50) * 0.001),
            'High': close_prices * (1 + abs(np.random.randn(50)) * 0.002),
            'Low': close_prices * (1 - abs(np.random.randn(50)) * 0.002),
            'Close': close_prices
        }, index=dates)
        
        # Ensure OHLC relationships are valid
        self.ohlc_data['High'] = np.maximum.reduce([
            self.ohlc_data['High'], 
            self.ohlc_data['Open'], 
            self.ohlc_data['Close']
        ])
        self.ohlc_data['Low'] = np.minimum.reduce([
            self.ohlc_data['Low'], 
            self.ohlc_data['Open'], 
            self.ohlc_data['Close']
        ])
    
    def test_atr_calculation(self):
        """Test basic ATR calculation."""
        atr = calculate_atr(self.ohlc_data, 14)
        
        self.assertIsInstance(atr, pd.Series)
        self.assertEqual(len(atr), len(self.ohlc_data))
        
        # Check that initial values are NaN
        self.assertTrue(pd.isna(atr.iloc[:13]).all())
        
        # Check that ATR values are positive
        valid_atr = atr.dropna()
        self.assertTrue((valid_atr > 0).all())
    
    def test_atr_indicator_class(self):
        """Test ATR indicator class."""
        indicator = ATRIndicator(14)
        atr = indicator.calculate(self.ohlc_data)
        
        self.assertIsInstance(atr, pd.Series)
        self.assertTrue(indicator.is_ready(20))
        self.assertEqual(indicator.get_warmup_period(), 14)
    
    def test_invalid_data(self):
        """Test ATR with invalid data."""
        # Missing columns
        invalid_data = self.ohlc_data.drop('High', axis=1)
        
        with self.assertRaises(ValueError):
            calculate_atr(invalid_data, 14)


class TestSignalGeneration(unittest.TestCase):
    """Test signal generation."""
    
    def setUp(self):
        """Set up test data."""
        dates = pd.date_range(start='2023-01-01', periods=200, freq='D')
        np.random.seed(42)
        
        # Create trending price data
        trend = np.linspace(1.0, 1.1, 200)
        noise = np.random.randn(200) * 0.005
        prices = trend + noise
        
        self.ohlc_data = pd.DataFrame({
            'Open': prices * 0.999,
            'High': prices * 1.001,
            'Low': prices * 0.999,
            'Close': prices
        }, index=dates)
        
        # Ensure OHLC relationships
        self.ohlc_data['High'] = np.maximum.reduce([
            self.ohlc_data['High'], 
            self.ohlc_data['Open'], 
            self.ohlc_data['Close']
        ])
        self.ohlc_data['Low'] = np.minimum.reduce([
            self.ohlc_data['Low'], 
            self.ohlc_data['Open'], 
            self.ohlc_data['Close']
        ])
    
    def test_crossover_signal_generation(self):
        """Test crossover signal generation."""
        signal_gen = CrossoverSignal(fast_period=10, slow_period=20)
        signals = signal_gen.generate_signals(self.ohlc_data)
        
        self.assertIsInstance(signals, pd.DataFrame)
        self.assertIn('Fast_EMA', signals.columns)
        self.assertIn('Slow_EMA', signals.columns)
        self.assertIn('Confirmed_Signal', signals.columns)
        
        # Check that signals are generated
        signal_count = (signals['Confirmed_Signal'] != 0).sum()
        self.assertGreater(signal_count, 0)
    
    def test_signal_generator_class(self):
        """Test main signal generator class."""
        config = {
            'strategy': {
                'ema_fast': 10,
                'ema_slow': 20
            }
        }
        
        signal_gen = SignalGenerator(config)
        signals = signal_gen.generate_all_signals(self.ohlc_data)
        
        self.assertIsInstance(signals, pd.DataFrame)
        self.assertIn('Trading_Signal', signals.columns)
        
        # Get signal summary
        summary = signal_gen.get_signal_summary(signals)
        self.assertIn('total_signals', summary)
        self.assertIn('long_signals', summary)
        self.assertIn('short_signals', summary)


if __name__ == '__main__':
    unittest.main()
