# Conservative Configuration Example
# Lower risk, more stable settings

trading:
  symbol: "AUDUSD"
  timeframe: "1h"                     # Higher timeframe for better signals
  initial_balance: 50000.0            # Larger account
  risk_per_trade: 0.01                # 1% risk per trade (conservative)
  min_profit_pips: 15                 # Higher profit threshold

strategy:
  name: "EMA_Cross_Conservative"
  ema_fast: 89                        # Slower EMAs for fewer, better signals
  ema_slow: 144
  min_ema_separation: 0.0005          # Higher separation requirement
  confirmation_bars: 3                # More confirmation needed

risk_management:
  max_martingale_orders: 3            # Fewer martingale levels
  atr_period: 20                      # Longer ATR period (smoother)
  atr_multiplier_target: 3.0          # Wider profit targets
  atr_multiplier_martingale: 2.0      # Wider martingale spacing
  max_drawdown_percent: 10.0          # Lower drawdown tolerance
  position_size_multiplier: 1.2       # Conservative size increases

data:
  source: "yfinance"
  start_date: "2020-01-01"            # Longer test period
  end_date: "2023-12-31"

backtesting:
  commission: 0.0001                  # Lower commission assumption
  spread: 0.00015                     # Standard AUD spread
  slippage: 0.00003                   # Lower slippage

reporting:
  output_dir: "results/conservative/"
