# Local Data Configuration Example
# This configuration uses local CSV files instead of online data sources

# Trading Parameters
trading:
  symbol: "AUDNZD"
  timeframe: "5m"
  initial_balance: 10000.0
  risk_per_trade: 0.02
  min_profit_pips: 5

# Strategy Parameters
strategy:
  name: "EMA_Cross_Local"
  ema_fast: 55
  ema_slow: 89
  min_ema_separation: 0.0001
  confirmation_bars: 1

# Risk Management
risk_management:
  max_martingale_orders: 5
  atr_period: 14
  atr_multiplier_target: 2.0
  atr_multiplier_martingale: 1.5
  position_size_multiplier: 1.5

# Local Data Settings
data:
  source: "local"                          # Use local CSV files
  start_date: "2023-01-01"
  end_date: "2023-02-01"                   # 31 days for meaningful backtesting
  
  # Local data configuration
  local_data_path: "examples/local_data/"  # Directory with CSV files
  local_file_pattern: "{symbol}_{timeframe}.csv"  # File naming pattern
  fallback_to_online: true                 # Fallback to online if file missing
  datetime_column: "Date"                  # Name of datetime column in CSV
  timezone: "UTC"                          # Timezone for datetime conversion

# Backtesting Settings
backtesting:
  commission: 0.0002
  spread: 0.00015
  slippage: 0.00005

# Visualization Settings
visualization:
  chart_width: 1200
  chart_height: 800
  show_volume: false
  show_indicators: true

# Reporting Settings
reporting:
  output_dir: "results/local_data/"
  generate_pdf: false
  generate_csv: true
  include_trade_log: true

# Logging Settings
logging:
  level: "INFO"
  file: "logs/local_data_backtest.log"
  console: true
