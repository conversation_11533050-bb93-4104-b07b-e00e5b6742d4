#!/usr/bin/env python3
"""
Example script demonstrating various ways to use the Forex Backtesting System.
"""

import sys
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from forex_backtest import load_config, BacktestEngine, EMACrossStrategy, DataLoader


def run_command_line_examples():
    """Demonstrate command line usage examples."""
    print("=== Command Line Examples ===")
    
    examples = [
        # Basic usage
        "python main.py",
        
        # Custom symbol and dates
        "python main.py --symbol EURUSD --start 2023-01-01 --end 2023-06-30",
        
        # Different balance
        "python main.py --balance 25000",
        
        # Custom config file
        "python main.py -c examples/conservative_config.yaml",
        
        # Quiet mode with custom output
        "python main.py --quiet --output-dir results/test",
        
        # Verbose mode for debugging
        "python main.py --verbose --symbol GBPUSD",
    ]
    
    for i, cmd in enumerate(examples, 1):
        print(f"{i}. {cmd}")
    
    print("\nTo run any example, copy and paste the command above.")


def run_programmatic_example():
    """Demonstrate programmatic usage."""
    print("\n=== Programmatic Example ===")
    
    try:
        # Load configuration
        config = load_config('examples/custom_config.yaml')
        print(f"Loaded config for {config['trading']['symbol']}")
        
        # Initialize components
        data_loader = DataLoader(config)
        
        # Load a small sample of data
        print("Loading sample data...")
        market_data = data_loader.load_data(
            symbol=config['trading']['symbol'],
            start_date='2023-01-01',
            end_date='2023-01-31',  # Just one month for demo
            timeframe=config['trading']['timeframe']
        )
        
        print(f"Loaded {len(market_data.data)} bars of data")
        
        # Initialize strategy
        strategy = EMACrossStrategy(config)
        
        # Generate signals (without running full backtest)
        print("Generating signals...")
        signals_df = strategy.generate_signals(market_data.data)
        
        # Count signals
        signal_count = (signals_df['Strategy_Signal'] != 0).sum()
        print(f"Generated {signal_count} trading signals")
        
        # Display signal summary
        if signal_count > 0:
            long_signals = (signals_df['Strategy_Signal'] > 0).sum()
            short_signals = (signals_df['Strategy_Signal'] < 0).sum()
            print(f"  - Long signals: {long_signals}")
            print(f"  - Short signals: {short_signals}")
        
        print("Programmatic example completed successfully!")
        
    except Exception as e:
        print(f"Error in programmatic example: {e}")


def run_batch_testing_example():
    """Demonstrate batch testing multiple configurations."""
    print("\n=== Batch Testing Example ===")
    
    configs = [
        'examples/conservative_config.yaml',
        'examples/custom_config.yaml', 
        'examples/aggressive_config.yaml'
    ]
    
    print("Batch testing configurations:")
    for i, config_file in enumerate(configs, 1):
        config_name = Path(config_file).stem
        print(f"{i}. {config_name}")
        
        # Command to run each config
        cmd = f"python main.py -c {config_file} --output-dir results/{config_name} --quiet"
        print(f"   Command: {cmd}")
    
    print("\nTo run batch tests, execute each command above in sequence.")


def demonstrate_config_customization():
    """Show how to customize configurations."""
    print("\n=== Configuration Customization ===")
    
    # Load base config
    config = load_config('config/settings.yaml')
    
    print("Original configuration:")
    print(f"  Symbol: {config['trading']['symbol']}")
    print(f"  EMA Fast: {config['strategy']['ema_fast']}")
    print(f"  EMA Slow: {config['strategy']['ema_slow']}")
    print(f"  Risk per trade: {config['trading']['risk_per_trade']}")
    
    # Modify configuration programmatically
    config['trading']['symbol'] = 'EURUSD'
    config['strategy']['ema_fast'] = 21
    config['strategy']['ema_slow'] = 55
    config['trading']['risk_per_trade'] = 0.03
    
    print("\nModified configuration:")
    print(f"  Symbol: {config['trading']['symbol']}")
    print(f"  EMA Fast: {config['strategy']['ema_fast']}")
    print(f"  EMA Slow: {config['strategy']['ema_slow']}")
    print(f"  Risk per trade: {config['trading']['risk_per_trade']}")
    
    # Save modified config
    from forex_backtest.config.settings import save_config
    save_config(config, 'examples/modified_config.yaml')
    print("\nSaved modified configuration to examples/modified_config.yaml")


def show_parameter_optimization_example():
    """Show how to test different parameters."""
    print("\n=== Parameter Optimization Example ===")
    
    # EMA period combinations to test
    ema_combinations = [
        (21, 55),
        (34, 89),
        (55, 89),
        (55, 144),
        (89, 144)
    ]
    
    print("EMA combinations to test:")
    for i, (fast, slow) in enumerate(ema_combinations, 1):
        print(f"{i}. Fast: {fast}, Slow: {slow}")
    
    # Risk levels to test
    risk_levels = [0.01, 0.02, 0.03, 0.05]
    
    print(f"\nRisk levels to test: {risk_levels}")
    
    # Martingale settings to test
    martingale_settings = [
        {'max_orders': 3, 'multiplier': 1.2},
        {'max_orders': 5, 'multiplier': 1.5},
        {'max_orders': 7, 'multiplier': 2.0}
    ]
    
    print("\nMartingale settings to test:")
    for i, setting in enumerate(martingale_settings, 1):
        print(f"{i}. Max orders: {setting['max_orders']}, Multiplier: {setting['multiplier']}")
    
    print("\nTo test these combinations, create separate config files for each")
    print("or modify the configuration programmatically in a loop.")


def main():
    """Run all examples."""
    print("Forex Backtesting System - Usage Examples")
    print("=" * 50)
    
    # Run examples
    run_command_line_examples()
    run_programmatic_example()
    run_batch_testing_example()
    demonstrate_config_customization()
    show_parameter_optimization_example()
    
    print("\n" + "=" * 50)
    print("Examples completed! Check the generated files and try the commands above.")


if __name__ == '__main__':
    main()
