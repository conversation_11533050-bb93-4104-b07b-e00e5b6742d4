# Custom Configuration Example for Forex Backtesting System
# This file demonstrates various configuration options

# Trading Parameters
trading:
  symbol: "AUDNZD"                    # Currency pair to trade
  timeframe: "15m"                    # Data timeframe (1m, 5m, 15m, 30m, 1h, 4h, 1d)
  initial_balance: 25000.0            # Starting account balance
  risk_per_trade: 0.015               # 1.5% risk per trade
  min_profit_pips: 8                  # Minimum profit in pips to close position

# Strategy Parameters
strategy:
  name: "EMA_Cross_Custom"
  ema_fast: 34                        # Fast EMA period (Fi<PERSON>acci number)
  ema_slow: 89                        # Slow EMA period (<PERSON><PERSON><PERSON>ci number)
  min_ema_separation: 0.0002          # Minimum separation between EMAs for valid signal
  confirmation_bars: 2                # Number of bars to confirm signal

# Risk Management (Conservative Settings)
risk_management:
  max_martingale_orders: 4            # Maximum martingale levels
  atr_period: 21                      # ATR calculation period
  atr_multiplier_target: 2.5          # ATR multiplier for profit targets
  atr_multiplier_martingale: 1.8      # ATR multiplier for martingale spacing
  max_drawdown_percent: 15.0          # Maximum allowed drawdown
  position_size_multiplier: 1.3       # Conservative martingale multiplier

# Data Settings
data:
  source: "yfinance"                  # Data source (yfinance, alphavantage, mt5)
  start_date: "2022-01-01"            # Backtest start date
  end_date: "2023-12-31"              # Backtest end date
  data_path: "data/"                  # Local data storage path

# Backtesting Settings
backtesting:
  commission: 0.00015                 # 0.015% commission per trade
  spread: 0.00012                     # 1.2 pip spread for EURUSD
  slippage: 0.00003                   # 0.3 pip slippage

# Visualization Settings
visualization:
  chart_width: 1400                   # Chart width in pixels
  chart_height: 900                   # Chart height in pixels
  show_volume: false                  # Show volume bars
  show_indicators: true               # Show technical indicators
  export_format: "html"               # Export format (html, png, pdf)

# Reporting Settings
reporting:
  output_dir: "results/custom/"       # Output directory for results
  generate_pdf: true                  # Generate PDF report
  generate_csv: true                  # Generate CSV files
  include_trade_log: true             # Include detailed trade log

# Logging Settings
logging:
  level: "INFO"                       # Logging level (DEBUG, INFO, WARNING, ERROR)
  file: "logs/custom_backtest.log"    # Log file path
  console: true                       # Enable console logging
