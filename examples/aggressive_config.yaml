# Aggressive Configuration Example
# Higher risk, higher potential reward settings

trading:
  symbol: "GBPUSD"
  timeframe: "5m"
  initial_balance: 10000.0
  risk_per_trade: 0.05                # 5% risk per trade (aggressive)
  min_profit_pips: 3                  # Lower profit threshold

strategy:
  name: "EMA_Cross_Aggressive"
  ema_fast: 21                        # Faster EMAs for more signals
  ema_slow: 55
  min_ema_separation: 0.00005         # Lower separation requirement
  confirmation_bars: 1                # Less confirmation needed

risk_management:
  max_martingale_orders: 7            # More martingale levels
  atr_period: 10                      # Shorter ATR period (more reactive)
  atr_multiplier_target: 1.5          # Closer profit targets
  atr_multiplier_martingale: 1.2      # Tighter martingale spacing
  max_drawdown_percent: 30.0          # Higher drawdown tolerance
  position_size_multiplier: 2.0       # Aggressive size increases

data:
  source: "yfinance"
  start_date: "2023-01-01"
  end_date: "2023-06-30"              # Shorter test period

backtesting:
  commission: 0.0003                  # Higher commission assumption
  spread: 0.0002                      # Higher spread for GBP pairs
  slippage: 0.00008                   # Higher slippage

reporting:
  output_dir: "results/aggressive/"
