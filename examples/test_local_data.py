#!/usr/bin/env python3
"""
Example script demonstrating local data usage with the Forex Backtesting System.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from forex_backtest import load_config, DataLoader
from forex_backtest.utils.validators import validate_local_csv_file, validate_local_data_directory


def test_local_data_loading():
    """Test loading data from local CSV files."""
    print("=== Testing Local Data Loading ===")
    
    # Load configuration for local data
    config = load_config('examples/local_data_config.yaml')
    
    # Initialize data loader
    data_loader = DataLoader(config)
    
    try:
        # Test loading AUDUSD data
        print("Loading AUDUSD 5m data from local file...")
        market_data = data_loader.load_data('AUDUSD', '2023-01-01', '2023-01-02', '5m')
        
        print(f"✅ Successfully loaded {len(market_data.data)} bars")
        print(f"   Date range: {market_data.data.index[0]} to {market_data.data.index[-1]}")
        print(f"   Columns: {list(market_data.data.columns)}")
        print(f"   Sample data:")
        print(market_data.data.head(3))
        
    except Exception as e:
        print(f"❌ Error loading local data: {e}")


def test_file_validation():
    """Test local file validation."""
    print("\n=== Testing File Validation ===")
    
    # Test individual file validation
    test_files = [
        'examples/local_data/AUDUSD_5m.csv',
        'examples/local_data/EURUSD_5m.csv'
    ]
    
    for file_path in test_files:
        print(f"\nValidating {file_path}...")
        errors = validate_local_csv_file(file_path)
        
        if not errors:
            print("✅ File is valid")
        else:
            print("❌ Validation errors:")
            for error in errors:
                print(f"   - {error}")


def test_directory_validation():
    """Test directory validation."""
    print("\n=== Testing Directory Validation ===")
    
    # Test directory validation
    result = validate_local_data_directory('examples/local_data/')
    
    print(f"Directory valid: {result['valid']}")
    print(f"Files found: {result['files_found']}")
    print(f"Valid files: {result['valid_files']}")
    
    if result['errors']:
        print("Errors:")
        for error in result['errors']:
            print(f"   - {error}")
    
    if result['warnings']:
        print("Warnings:")
        for warning in result['warnings']:
            print(f"   - {warning}")
    
    print("\nFile details:")
    for file_info in result['files']:
        status = "✅" if file_info['valid'] else "❌"
        print(f"   {status} {file_info['filename']}")
        if file_info['errors']:
            for error in file_info['errors']:
                print(f"      - {error}")


def test_list_available_files():
    """Test listing available local files."""
    print("\n=== Testing File Listing ===")
    
    config = load_config('examples/local_data_config.yaml')
    data_loader = DataLoader(config)
    
    files = data_loader.list_local_files()
    
    if files:
        print(f"Found {len(files)} local data files:")
        print(f"{'Filename':<25} {'Symbol':<8} {'Timeframe':<10} {'Size (KB)':<10}")
        print("-" * 55)
        
        for file in files:
            size_kb = file['size'] / 1024
            print(f"{file['filename']:<25} {file['symbol']:<8} {file['timeframe']:<10} {size_kb:<10.1f}")
    else:
        print("No local data files found")


def test_fallback_mechanism():
    """Test fallback to online data."""
    print("\n=== Testing Fallback Mechanism ===")
    
    # Create config with fallback enabled
    config = {
        'data': {
            'source': 'local',
            'local_data_path': 'examples/local_data/',
            'local_file_pattern': '{symbol}_{timeframe}.csv',
            'fallback_to_online': True,
            'start_date': '2023-01-01',
            'end_date': '2023-01-02'
        },
        'trading': {
            'symbol': 'GBPUSD',  # File doesn't exist
            'timeframe': '5m'
        }
    }
    
    data_loader = DataLoader(config)
    
    try:
        print("Attempting to load GBPUSD (file doesn't exist, should fallback)...")
        market_data = data_loader.load_data('GBPUSD', '2023-01-01', '2023-01-02', '5m')
        print(f"✅ Fallback successful: loaded {len(market_data.data)} bars")
        
    except Exception as e:
        print(f"❌ Fallback failed: {e}")


def demonstrate_command_line_usage():
    """Demonstrate command line usage examples."""
    print("\n=== Command Line Usage Examples ===")
    
    examples = [
        "# Use local data with default settings",
        "python main.py --local-data",
        "",
        "# Specify custom data directory",
        "python main.py --local-data --data-dir examples/local_data/",
        "",
        "# Use custom file pattern",
        "python main.py --local-data --file-pattern '{symbol}_{timeframe}_custom.csv'",
        "",
        "# List available local files",
        "python main.py --list-local-files",
        "",
        "# Validate specific local file",
        "python main.py --validate-local AUDUSD 5m",
        "",
        "# Use local data with configuration file",
        "python main.py -c examples/local_data_config.yaml",
        "",
        "# Combine local data with other options",
        "python main.py --symbol EURUSD --local-data --start 2023-01-01 --end 2023-01-02"
    ]
    
    for example in examples:
        print(example)


def main():
    """Run all local data tests."""
    print("Local Data Testing Suite")
    print("=" * 50)
    
    # Ensure example data directory exists
    os.makedirs('examples/local_data', exist_ok=True)
    
    # Run tests
    test_local_data_loading()
    test_file_validation()
    test_directory_validation()
    test_list_available_files()
    test_fallback_mechanism()
    demonstrate_command_line_usage()
    
    print("\n" + "=" * 50)
    print("Local data testing completed!")
    print("\nTo run a backtest with local data:")
    print("python main.py -c examples/local_data_config.yaml")


if __name__ == '__main__':
    main()
