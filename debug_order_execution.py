#!/usr/bin/env python3
"""
Debug script to analyze order execution issues.
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from forex_backtest import load_config, DataLoader, BacktestEngine, EMACrossStrategy
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


def debug_order_execution():
    """Debug order execution step by step."""
    print("🔍 DEBUGGING ORDER EXECUTION")
    print("=" * 50)
    
    # Load configuration
    config = load_config('config/settings.yaml')
    
    # Override dates to use 2024 data
    config['data']['start_date'] = '2024-01-01'
    config['data']['end_date'] = '2024-12-31'
    config['trading']['timeframe'] = '1d'
    
    try:
        # Load data
        data_loader = DataLoader({
            'data': {
                'source': 'yfinance',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31'
            }
        })
        
        market_data = data_loader.load_data('AUDNZD', '2024-01-01', '2024-12-31', '1d')
        print(f"✅ Data loaded: {len(market_data.data)} bars")
        
        # Initialize strategy
        strategy = EMACrossStrategy(config)
        
        # Generate signals
        signals_df = strategy.generate_signals(market_data.data)
        strategy_signals = (signals_df['Strategy_Signal'] != 0).sum()
        print(f"✅ Strategy signals generated: {strategy_signals}")
        
        # Show signal details
        if strategy_signals > 0:
            signal_indices = signals_df.index[signals_df['Strategy_Signal'] != 0]
            print(f"Signal dates: {[date.strftime('%Y-%m-%d') for date in signal_indices]}")
        
        # Initialize backtest engine with debug logging
        backtest_engine = BacktestEngine(strategy, market_data, config)
        
        # Manually step through first few bars to debug
        print(f"\n🔍 MANUAL BAR PROCESSING")
        print("=" * 30)
        
        data = market_data.data
        signals = signals_df['Strategy_Signal']
        
        # Find first signal bar
        signal_bar_indices = signals[signals != 0].index
        
        if len(signal_bar_indices) > 0:
            first_signal_date = signal_bar_indices[0]
            first_signal_index = data.index.get_loc(first_signal_date)
            
            print(f"First signal at: {first_signal_date} (index {first_signal_index})")
            print(f"Signal value: {signals.loc[first_signal_date]}")
            
            # Process bars up to and including first signal
            for i in range(max(0, first_signal_index - 2), min(len(data), first_signal_index + 3)):
                bar_date = data.index[i]
                bar_data = data.iloc[i]
                current_signals = signals.iloc[i] if i < len(signals) else 0
                
                print(f"\n📊 Processing bar {i}: {bar_date.strftime('%Y-%m-%d')}")
                print(f"   OHLC: O={bar_data['Open']:.5f}, H={bar_data['High']:.5f}, "
                      f"L={bar_data['Low']:.5f}, C={bar_data['Close']:.5f}")
                print(f"   Signal: {current_signals}")
                
                # Check what strategy does with this bar
                orders_before = len(strategy.orders)
                positions_before = len(strategy.position_manager.positions)
                
                # Simulate strategy.on_bar call
                new_orders = strategy.on_bar(bar_data, i, pd.Series([current_signals], index=[bar_date]))
                
                orders_after = len(strategy.orders)
                positions_after = len(strategy.position_manager.positions)
                
                print(f"   Orders before/after: {orders_before}/{orders_after}")
                print(f"   Positions before/after: {positions_before}/{positions_after}")
                print(f"   New orders generated: {len(new_orders)}")
                
                if len(new_orders) > 0:
                    for order in new_orders:
                        print(f"   📝 Order: {order.order_id} - {order.side.value} {order.size} at {order.order_type.value}")
                        
                        # Simulate order fill
                        fill_price = bar_data['Close']  # Use close price for fill
                        print(f"   💰 Filling order at {fill_price:.5f}")
                        
                        # Call strategy fill_order
                        strategy.fill_order(order.order_id, fill_price, bar_date)
                        
                        positions_after_fill = len(strategy.position_manager.positions)
                        print(f"   Positions after fill: {positions_after_fill}")
                        
                        # Check if position was created
                        if positions_after_fill > positions_after:
                            print(f"   ✅ Position created successfully!")
                        else:
                            print(f"   ❌ No position created")
                
                if i == first_signal_index:
                    break
        
        # Now run full backtest to see results
        print(f"\n🚀 RUNNING FULL BACKTEST")
        print("=" * 30)
        
        # Reset strategy state
        strategy = EMACrossStrategy(config)
        backtest_engine = BacktestEngine(strategy, market_data, config)
        
        results = backtest_engine.run()
        
        # Display results
        performance = results.performance_summary
        
        print(f"\n📈 Final Results:")
        print(f"  Total Trades: {performance['total_trades']}")
        print(f"  Final Equity: ${performance['final_equity']:,.2f}")
        print(f"  Total Return: {performance['total_return_percent']:+.2f}%")
        
        # Check execution log
        if hasattr(backtest_engine, 'execution_log') and len(backtest_engine.execution_log) > 0:
            print(f"\n📋 Execution Log:")
            for i, execution in enumerate(backtest_engine.execution_log[:5]):
                print(f"  {i+1}. {execution['timestamp'].strftime('%Y-%m-%d')}: "
                      f"{execution['side']} {execution['size']} at {execution['fill_price']:.5f}")
        else:
            print(f"\n❌ No executions in log")
        
        # Check final positions
        final_positions = strategy.get_open_positions()
        print(f"\nFinal open positions: {len(final_positions)}")
        
        return performance['total_trades'] > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run order execution debugging."""
    print("🚀 AUDNZD ORDER EXECUTION DEBUG")
    print("=" * 60)
    
    success = debug_order_execution()
    
    if success:
        print(f"\n✅ SUCCESS: Orders are being executed!")
    else:
        print(f"\n❌ ISSUE: Orders are not being executed properly.")
        print("Check the debug output above for details.")


if __name__ == '__main__':
    main()
