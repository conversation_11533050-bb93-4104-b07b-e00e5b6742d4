#!/usr/bin/env python3
"""
Main execution script for Forex Backtesting System.

This script provides a command-line interface for running forex backtests
with the EMA crossover strategy and martingale position management.
"""

import sys
import argparse
import logging
from pathlib import Path
from typing import Optional

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from forex_backtest import (
    load_config, BacktestEngine, EMACrossStrategy, 
    DataLoader, ChartGenerator, ReportGenerator
)
from forex_backtest.utils.helpers import setup_logging, print_banner, print_summary_table
from forex_backtest.utils.validators import validate_config, validate_backtest_parameters


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Forex Backtesting System - EMA Crossover Strategy",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Run with default config
  python main.py -c custom_config.yaml    # Use custom configuration
  python main.py --symbol EURUSD          # Override symbol
  python main.py --start 2023-01-01 --end 2023-12-31  # Custom date range
  python main.py --local-data --data-dir data/mt5/     # Use local CSV files
  python main.py --list-local-files       # List available local files
  python main.py --validate-local AUDUSD 5m  # Validate local data file
  python main.py --no-charts              # Skip chart generation
  python main.py --verbose                # Enable debug logging
        """
    )
    
    parser.add_argument(
        '-c', '--config',
        type=str,
        default='config/settings.yaml',
        help='Configuration file path (default: config/settings.yaml)'
    )
    
    parser.add_argument(
        '--symbol',
        type=str,
        help='Trading symbol (overrides config)'
    )
    
    parser.add_argument(
        '--start',
        type=str,
        help='Start date YYYY-MM-DD (overrides config)'
    )
    
    parser.add_argument(
        '--end',
        type=str,
        help='End date YYYY-MM-DD (overrides config)'
    )
    
    parser.add_argument(
        '--balance',
        type=float,
        help='Initial balance (overrides config)'
    )
    
    parser.add_argument(
        '--no-charts',
        action='store_true',
        help='Skip chart generation'
    )
    
    parser.add_argument(
        '--no-reports',
        action='store_true',
        help='Skip report generation'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        help='Output directory for results'
    )

    parser.add_argument(
        '--local-data',
        action='store_true',
        help='Use local CSV files instead of online data'
    )

    parser.add_argument(
        '--data-dir',
        type=str,
        help='Directory containing local CSV files'
    )

    parser.add_argument(
        '--file-pattern',
        type=str,
        help='File naming pattern for local files (e.g., {symbol}_{timeframe}.csv)'
    )

    parser.add_argument(
        '--list-local-files',
        action='store_true',
        help='List available local data files and exit'
    )

    parser.add_argument(
        '--validate-local',
        type=str,
        nargs=2,
        metavar=('SYMBOL', 'TIMEFRAME'),
        help='Validate local data file for symbol and timeframe'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Suppress console output'
    )
    
    return parser.parse_args()


def load_and_validate_config(config_path: str, args) -> dict:
    """Load and validate configuration."""
    try:
        config = load_config(config_path)
        
        # Apply command line overrides
        if args.symbol:
            config['trading']['symbol'] = args.symbol
        
        if args.start:
            config['data']['start_date'] = args.start
        
        if args.end:
            config['data']['end_date'] = args.end
        
        if args.balance:
            config['trading']['initial_balance'] = args.balance
        
        if args.output_dir:
            config['reporting']['output_dir'] = args.output_dir

        # Apply local data overrides
        if args.local_data:
            config['data']['source'] = 'local'

        if args.data_dir:
            config['data']['local_data_path'] = args.data_dir

        if args.file_pattern:
            config['data']['local_file_pattern'] = args.file_pattern
        
        # Validate configuration
        validation_errors = validate_config(config)
        if validation_errors:
            print("Configuration validation errors:")
            for error in validation_errors:
                print(f"  - {error}")
            sys.exit(1)
        
        return config
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        sys.exit(1)


def run_backtest(config: dict, args) -> tuple:
    """Run the backtest and return results."""
    try:
        # Initialize components
        print("Initializing backtesting components...")
        
        # Data loader
        data_loader = DataLoader(config)
        
        # Load market data
        symbol = config['trading']['symbol']
        start_date = config['data']['start_date']
        end_date = config['data']['end_date']
        timeframe = config['trading']['timeframe']
        
        print(f"Loading market data for {symbol} ({timeframe}) from {start_date} to {end_date}...")
        market_data = data_loader.load_data(symbol, start_date, end_date, timeframe)
        
        print(f"Loaded {len(market_data.data)} bars of data")
        
        # Initialize strategy
        print("Initializing EMA crossover strategy...")
        strategy = EMACrossStrategy(config)
        
        # Initialize backtest engine
        print("Initializing backtest engine...")
        backtest_engine = BacktestEngine(strategy, market_data, config)
        
        # Run backtest
        print("Running backtest...")
        results = backtest_engine.run()
        
        return results, backtest_engine
        
    except Exception as e:
        logging.error(f"Backtest execution failed: {e}")
        raise


def generate_outputs(results, config: dict, args):
    """Generate charts and reports."""
    generated_files = {}
    
    try:
        # Generate charts
        if not args.no_charts:
            print("Generating interactive charts...")
            chart_generator = ChartGenerator(results, config)
            
            # Create trading chart
            trading_chart = chart_generator.create_trading_chart()
            chart_file = Path(config['reporting']['output_dir']) / 'trading_chart.html'
            chart_generator.save_chart(trading_chart, str(chart_file), 'html')
            generated_files['trading_chart'] = str(chart_file)
            
            # Create performance dashboard
            dashboard = chart_generator.create_performance_dashboard()
            dashboard_file = Path(config['reporting']['output_dir']) / 'performance_dashboard.html'
            chart_generator.save_chart(dashboard, str(dashboard_file), 'html')
            generated_files['dashboard'] = str(dashboard_file)
            
            print(f"Charts saved to {config['reporting']['output_dir']}")
        
        # Generate reports
        if not args.no_reports:
            print("Generating comprehensive reports...")
            report_generator = ReportGenerator(results, config)
            
            report_files = report_generator.generate_comprehensive_report()
            generated_files.update(report_files)
            
            print(f"Reports saved to {config['reporting']['output_dir']}")
        
        return generated_files
        
    except Exception as e:
        logging.error(f"Output generation failed: {e}")
        raise


def display_summary(results):
    """Display backtest summary."""
    performance = results.performance_summary
    
    print_banner("BACKTEST RESULTS SUMMARY")
    
    # Key metrics
    summary_data = {
        'Symbol': results.market_data.symbol,
        'Timeframe': results.market_data.timeframe,
        'Period': f"{results.market_data.data.index[0].strftime('%Y-%m-%d')} to {results.market_data.data.index[-1].strftime('%Y-%m-%d')}",
        'Total Bars': len(results.market_data.data),
        'Initial Balance': f"${performance['initial_balance']:,.2f}",
        'Final Equity': f"${performance['final_equity']:,.2f}",
        'Total Return': f"{performance['total_return_percent']:+.2f}%",
        'Annualized Return': f"{performance['annualized_return_percent']:+.2f}%",
        'Max Drawdown': f"{performance['max_drawdown_percent']:.2f}%",
        'Sharpe Ratio': f"{performance['sharpe_ratio']:.2f}",
        'Calmar Ratio': f"{performance['calmar_ratio']:.2f}",
        'Total Trades': performance['total_trades'],
        'Win Rate': f"{performance['win_rate_percent']:.1f}%",
        'Profit Factor': f"{performance['profit_factor']:.2f}",
        'Average Trade': f"${performance['average_trade']['average']:.2f}",
    }
    
    print_summary_table(summary_data)


def list_local_files(config):
    """List available local data files."""
    data_loader = DataLoader(config)
    files = data_loader.list_local_files()

    if not files:
        print("No local data files found.")
        return

    print(f"Found {len(files)} local data files:")
    print(f"{'Filename':<30} {'Symbol':<10} {'Timeframe':<10} {'Size (KB)':<10} {'Modified':<20}")
    print("-" * 80)

    for file in files:
        size_kb = file['size'] / 1024
        modified = file['modified'].strftime('%Y-%m-%d %H:%M:%S')
        print(f"{file['filename']:<30} {file['symbol']:<10} {file['timeframe']:<10} {size_kb:<10.1f} {modified:<20}")


def validate_local_file(config, symbol, timeframe):
    """Validate a local data file."""
    data_loader = DataLoader(config)
    result = data_loader.validate_local_file(symbol, timeframe)

    print(f"Validation result for {symbol} {timeframe}:")
    print(f"  Valid: {result['valid']}")
    print(f"  Message: {result['message']}")

    if result['valid']:
        print(f"  Sample bars: {result['sample_bars']}")
        print(f"  Columns: {', '.join(result['columns'])}")
        if result['date_range']['start']:
            print(f"  Date range: {result['date_range']['start']} to {result['date_range']['end']}")


def main():
    """Main execution function."""
    # Parse arguments
    args = parse_arguments()

    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    console_logging = not args.quiet

    setup_logging(
        level=log_level,
        log_file="logs/backtest.log",
        console=console_logging
    )

    logger = logging.getLogger(__name__)

    try:
        print_banner("FOREX BACKTESTING SYSTEM")
        print("EMA Crossover Strategy with Martingale Position Management")
        print()

        # Load and validate configuration
        print(f"Loading configuration from {args.config}...")
        config = load_and_validate_config(args.config, args)

        # Handle special commands
        if args.list_local_files:
            list_local_files(config)
            return 0

        if args.validate_local:
            symbol, timeframe = args.validate_local
            validate_local_file(config, symbol, timeframe)
            return 0
        
        # Validate backtest parameters
        validation_errors = validate_backtest_parameters(
            config['data']['start_date'],
            config['data']['end_date'],
            config['trading']['initial_balance'],
            config['trading']['symbol']
        )
        
        if validation_errors:
            print("Backtest parameter validation errors:")
            for error in validation_errors:
                print(f"  - {error}")
            sys.exit(1)
        
        # Run backtest
        results, backtest_engine = run_backtest(config, args)
        
        # Display summary
        if not args.quiet:
            display_summary(results)
        
        # Generate outputs
        generated_files = generate_outputs(results, config, args)
        
        # Display generated files
        if generated_files and not args.quiet:
            print("\nGenerated Files:")
            for file_type, file_path in generated_files.items():
                print(f"  {file_type}: {file_path}")
        
        print("\nBacktest completed successfully!")
        
        # Return success
        return 0
        
    except KeyboardInterrupt:
        print("\nBacktest interrupted by user")
        return 1
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
