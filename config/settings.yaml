# Forex Backtesting System Configuration

# Trading Parameters
trading:
  symbol: "AUDUSD"
  timeframe: "5m"  # 5-minute bars
  initial_balance: 10000.0
  risk_per_trade: 0.02  # 2% risk per trade
  min_profit_pips: 5    # Minimum profit to close position
  
# Strategy Parameters
strategy:
  name: "EMA_Cross"
  ema_fast: 55
  ema_slow: 89
  
# Risk Management
risk_management:
  max_martingale_orders: 5
  atr_period: 14
  atr_multiplier_target: 2.0
  atr_multiplier_martingale: 1.5
  max_drawdown_percent: 20.0
  position_size_multiplier: 1.5  # Martingale multiplier
  
# Data Settings
data:
  source: "yfinance"  # Options: yfinance, mt5, alphavantage
  start_date: "2023-01-01"
  end_date: "2024-01-01"
  data_path: "data/"
  
# Backtesting Settings
backtesting:
  commission: 0.0002  # 0.02% commission
  spread: 0.00015     # 1.5 pip spread for AUDUSD
  slippage: 0.00005   # 0.5 pip slippage
  
# Visualization Settings
visualization:
  chart_width: 1200
  chart_height: 800
  show_volume: false
  show_indicators: true
  export_format: "html"  # Options: html, png, pdf
  
# Reporting Settings
reporting:
  output_dir: "results/"
  generate_pdf: true
  generate_csv: true
  include_trade_log: true
  
# Logging Settings
logging:
  level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR
  file: "logs/backtest.log"
  console: true
