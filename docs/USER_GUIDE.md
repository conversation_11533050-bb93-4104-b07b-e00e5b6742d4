# Forex Backtesting System - User Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Running Backtests](#running-backtests)
5. [Understanding Results](#understanding-results)
6. [Advanced Usage](#advanced-usage)
7. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Your First Backtest
```bash
python main.py
```

This will run a backtest with default settings:
- Symbol: AUDUSD
- Timeframe: 5 minutes
- Period: 2023-01-01 to 2024-01-01
- Strategy: EMA 55/89 crossover with martingale

### 3. View Results
Results will be saved in the `results/` directory:
- `trading_chart.html` - Interactive price chart with signals
- `performance_dashboard.html` - Performance metrics dashboard
- `backtest_report.html` - Comprehensive HTML report
- `trade_history.csv` - Detailed trade log
- `backtest_summary.json` - Key metrics in JSON format

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Required Dependencies
```bash
pip install pandas numpy plotly yfinance pyyaml tqdm colorama jinja2
```

### Optional Dependencies
For advanced features:
```bash
# For TA-Lib indicators (optional)
pip install ta-lib

# For PDF report generation
pip install weasyprint

# For development and testing
pip install pytest pytest-cov black flake8 mypy
```

### Installation from Source
```bash
git clone <repository-url>
cd forex-backtest
pip install -r requirements.txt
```

## Configuration

### Configuration File Structure
The system uses YAML configuration files. The main configuration is in `config/settings.yaml`:

```yaml
# Trading Parameters
trading:
  symbol: "AUDUSD"
  timeframe: "5m"
  initial_balance: 10000.0
  risk_per_trade: 0.02
  min_profit_pips: 5

# Strategy Parameters  
strategy:
  name: "EMA_Cross"
  ema_fast: 55
  ema_slow: 89

# Risk Management
risk_management:
  max_martingale_orders: 5
  atr_period: 14
  atr_multiplier_target: 2.0
  atr_multiplier_martingale: 1.5
  position_size_multiplier: 1.5

# Data Settings
data:
  source: "yfinance"
  start_date: "2023-01-01"
  end_date: "2024-01-01"
```

### Key Configuration Parameters

#### Trading Parameters
- `symbol`: Currency pair to trade (AUDUSD, EURUSD, etc.)
- `timeframe`: Data timeframe (1m, 5m, 15m, 30m, 1h, 4h, 1d)
- `initial_balance`: Starting account balance
- `risk_per_trade`: Risk percentage per trade (0.02 = 2%)
- `min_profit_pips`: Minimum profit in pips to close position

#### Strategy Parameters
- `ema_fast`: Fast EMA period (default: 55)
- `ema_slow`: Slow EMA period (default: 89)

#### Risk Management
- `max_martingale_orders`: Maximum martingale levels (1-10)
- `atr_period`: ATR calculation period
- `atr_multiplier_target`: ATR multiplier for profit targets
- `atr_multiplier_martingale`: ATR multiplier for martingale spacing
- `position_size_multiplier`: Size multiplier for martingale orders

## Running Backtests

### Basic Usage
```bash
# Run with default configuration
python main.py

# Use custom configuration file
python main.py -c my_config.yaml

# Override specific parameters
python main.py --symbol EURUSD --start 2023-06-01 --end 2023-12-31

# Run with different initial balance
python main.py --balance 50000
```

### Command Line Options
```bash
python main.py [OPTIONS]

Options:
  -c, --config PATH        Configuration file path
  --symbol TEXT           Trading symbol (overrides config)
  --start DATE            Start date YYYY-MM-DD
  --end DATE              End date YYYY-MM-DD  
  --balance FLOAT         Initial balance
  --no-charts             Skip chart generation
  --no-reports            Skip report generation
  --output-dir PATH       Output directory for results
  --verbose, -v           Enable verbose logging
  --quiet, -q             Suppress console output
```

### Examples

#### Test Different Symbols
```bash
python main.py --symbol EURUSD
python main.py --symbol GBPUSD
python main.py --symbol USDJPY
```

#### Test Different Time Periods
```bash
# Test recent period
python main.py --start 2023-01-01 --end 2023-06-30

# Test longer period
python main.py --start 2022-01-01 --end 2023-12-31

# Test specific volatile period
python main.py --start 2020-03-01 --end 2020-06-30
```

#### Test Different Account Sizes
```bash
# Small account
python main.py --balance 1000

# Large account  
python main.py --balance 100000
```

## Understanding Results

### Performance Metrics

#### Key Metrics
- **Total Return**: Overall percentage return
- **Annualized Return**: Return adjusted for time period
- **Max Drawdown**: Largest peak-to-trough decline
- **Sharpe Ratio**: Risk-adjusted return measure
- **Calmar Ratio**: Return divided by max drawdown
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit divided by gross loss

#### Risk Metrics
- **Value at Risk (VaR)**: Potential loss at confidence levels
- **Sortino Ratio**: Downside risk-adjusted return
- **Maximum Adverse Excursion**: Worst unrealized loss
- **Recovery Factor**: Ability to recover from drawdowns

#### Trade Analysis
- **Total Trades**: Number of completed trades
- **Average Trade**: Average profit/loss per trade
- **Consecutive Wins/Losses**: Longest winning/losing streaks
- **Martingale Usage**: Statistics on martingale system usage

### Chart Interpretation

#### Price Chart Elements
- **Candlesticks**: OHLC price data
- **EMA Lines**: Fast (blue) and slow (orange) EMAs
- **Entry Signals**: Green triangles (long), red triangles (short)
- **Exit Signals**: Green circles (profit), red circles (loss)
- **ATR Bands**: Gray dashed lines showing volatility

#### Equity Curve
- **Blue Line**: Account equity over time
- **Red Line**: Drawdown percentage
- **Smooth Upward Trend**: Consistent profitability
- **Sharp Drops**: Significant losses or drawdown periods

### Report Sections

#### HTML Report
1. **Performance Summary**: Key metrics overview
2. **Detailed Metrics**: Comprehensive statistics
3. **Trade History**: Recent trade details
4. **Charts**: Interactive visualizations

#### CSV Files
- `trade_history.csv`: Complete trade log with entry/exit details
- `equity_curve.csv`: Equity and drawdown over time
- `monthly_returns.csv`: Monthly performance breakdown

## Advanced Usage

### Custom Configuration Files

Create specialized configurations for different scenarios:

```yaml
# config/aggressive.yaml - Higher risk configuration
trading:
  risk_per_trade: 0.05  # 5% risk
risk_management:
  max_martingale_orders: 7
  position_size_multiplier: 2.0

# config/conservative.yaml - Lower risk configuration  
trading:
  risk_per_trade: 0.01  # 1% risk
risk_management:
  max_martingale_orders: 3
  position_size_multiplier: 1.2
```

### Batch Testing

Test multiple configurations:
```bash
# Test different symbols
for symbol in AUDUSD EURUSD GBPUSD; do
  python main.py --symbol $symbol --output-dir results_$symbol
done

# Test different periods
for year in 2020 2021 2022 2023; do
  python main.py --start ${year}-01-01 --end ${year}-12-31 --output-dir results_$year
done
```

### Parameter Optimization

Test different EMA periods by modifying configuration:
```yaml
strategy:
  ema_fast: 21    # Test different values: 21, 34, 55
  ema_slow: 55    # Test different values: 55, 89, 144
```

### Integration with Other Tools

Export results for further analysis:
```python
import pandas as pd

# Load trade history
trades = pd.read_csv('results/trade_history.csv')

# Analyze by time of day
trades['hour'] = pd.to_datetime(trades['entry_timestamp']).dt.hour
hourly_performance = trades.groupby('hour')['pnl'].mean()

# Analyze by day of week
trades['weekday'] = pd.to_datetime(trades['entry_timestamp']).dt.dayofweek
daily_performance = trades.groupby('weekday')['pnl'].mean()
```

## Troubleshooting

### Common Issues

#### Data Loading Problems
```
Error: No data returned for AUDUSD
```
**Solution**: Check internet connection and symbol availability. Try different date ranges.

#### Configuration Errors
```
Configuration validation failed: Fast EMA period must be less than slow EMA period
```
**Solution**: Ensure fast EMA < slow EMA in configuration.

#### Memory Issues
```
MemoryError: Unable to allocate array
```
**Solution**: Reduce date range or use higher timeframe (e.g., 1h instead of 5m).

#### Missing Dependencies
```
ModuleNotFoundError: No module named 'plotly'
```
**Solution**: Install missing dependencies:
```bash
pip install plotly
```

### Performance Optimization

#### For Large Datasets
- Use higher timeframes (1h, 4h, 1d)
- Reduce date range
- Disable chart generation with `--no-charts`

#### For Faster Testing
- Use smaller date ranges for initial testing
- Test with single symbol before batch processing
- Use `--quiet` flag to reduce output

### Getting Help

#### Log Files
Check `logs/backtest.log` for detailed error information:
```bash
tail -f logs/backtest.log
```

#### Verbose Mode
Run with verbose logging for debugging:
```bash
python main.py --verbose
```

#### Validation
Test configuration before running:
```python
from forex_backtest.config.settings import load_config, validate_config

config = load_config('config/settings.yaml')
validate_config(config)  # Will raise exception if invalid
```
