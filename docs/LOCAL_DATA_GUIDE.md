# Local Data Guide - Using Your Own CSV Files

This guide explains how to use your own historical forex data files with the Forex Backtesting System instead of downloading data from online sources.

## Overview

The local data feature allows you to:
- Use CSV files exported from MetaTrader 4/5
- Import data from TradingView or other platforms
- Work offline without internet connectivity
- Use custom or proprietary data sources
- Maintain consistent data across multiple backtests

## Supported File Formats

### CSV File Requirements

Your CSV files must contain the following columns (case-insensitive):
- **Date/Datetime/Time**: Timestamp column
- **Open**: Opening price
- **High**: Highest price
- **Low**: Lowest price
- **Close**: Closing price
- **Volume** (optional): Trading volume

### Supported Datetime Formats

The system automatically detects common datetime formats:
- `2023-01-01 09:00:00`
- `2023-01-01 09:00`
- `01/01/2023 09:00:00`
- `2023.01.01 09:00:00`
- Unix timestamps

### File Naming Conventions

The system supports multiple naming patterns:

#### Standard Pattern (Default)
```
{symbol}_{timeframe}.csv
```
Examples:
- `AUDUSD_5m.csv`
- `EURUSD_1h.csv`
- `GBPUSD_1d.csv`

#### MetaTrader Pattern
```
{symbol}{timeframe}.csv
```
Examples:
- `AUDUSD5m.csv`
- `EURUSD1h.csv`

#### TradingView Pattern
```
{symbol}_{timeframe}_TradingView.csv
```
Examples:
- `AUDUSD_5m_TradingView.csv`
- `EURUSD_1h_TradingView.csv`

#### Custom Pattern
You can define your own pattern using placeholders:
- `{symbol}`: Currency pair (e.g., AUDUSD)
- `{timeframe}`: Time interval (e.g., 5m, 1h)

## Configuration

### Using Configuration File

Add local data settings to your `config/settings.yaml`:

```yaml
data:
  source: "local"                           # Use local CSV files
  local_data_path: "data/local/"           # Directory containing CSV files
  local_file_pattern: "{symbol}_{timeframe}.csv"  # File naming pattern
  fallback_to_online: true                 # Fallback to online if file not found
  datetime_column: "Date"                  # Name of datetime column
  timezone: "UTC"                          # Timezone for datetime conversion
  start_date: "2023-01-01"
  end_date: "2023-12-31"
```

### Using Command Line

```bash
# Use local data with default settings
python main.py --local-data

# Specify custom data directory
python main.py --local-data --data-dir data/mt5_exports/

# Use custom file pattern
python main.py --local-data --file-pattern "{symbol}_{timeframe}_custom.csv"

# Combine with other options
python main.py --symbol EURUSD --local-data --data-dir data/custom/ --start 2023-06-01
```

## Directory Structure

Organize your local data files in a dedicated directory:

```
data/
├── local/                    # Default local data directory
│   ├── AUDUSD_5m.csv        # 5-minute AUDUSD data
│   ├── AUDUSD_1h.csv        # 1-hour AUDUSD data
│   ├── EURUSD_5m.csv        # 5-minute EURUSD data
│   ├── EURUSD_1h.csv        # 1-hour EURUSD data
│   └── GBPUSD_5m.csv        # 5-minute GBPUSD data
├── mt5_exports/             # MetaTrader 5 exports
│   ├── AUDUSD5m.csv
│   └── EURUSD5m.csv
└── tradingview/             # TradingView exports
    ├── AUDUSD_5m_TradingView.csv
    └── EURUSD_5m_TradingView.csv
```

## Data Export Instructions

### From MetaTrader 4/5

1. **Open MetaTrader** and select your desired symbol
2. **Set the timeframe** (M5, M15, H1, etc.)
3. **Right-click on the chart** → "Save as" → Choose CSV format
4. **Save the file** using the naming convention: `{SYMBOL}{TIMEFRAME}.csv`
5. **Move the file** to your local data directory

### From TradingView

1. **Open TradingView** and load your chart
2. **Set the desired timeframe**
3. **Click the "..." menu** → "Export chart data"
4. **Download the CSV file**
5. **Rename the file** to match your naming pattern
6. **Place in your local data directory**

### From Other Platforms

Most trading platforms support CSV export. Ensure your exported file contains:
- Datetime column (first column recommended)
- OHLC columns with proper headers
- Consistent datetime format
- No missing data gaps

## Example CSV File Format

### Standard Format
```csv
Date,Open,High,Low,Close,Volume
2023-01-01 00:00:00,1.0500,1.0520,1.0495,1.0510,1000
2023-01-01 00:05:00,1.0510,1.0525,1.0505,1.0515,1200
2023-01-01 00:10:00,1.0515,1.0530,1.0510,1.0520,800
```

### MetaTrader Format
```csv
Date,Time,Open,High,Low,Close,Volume
2023.01.01,00:00,1.0500,1.0520,1.0495,1.0510,1000
2023.01.01,00:05,1.0510,1.0525,1.0505,1.0515,1200
2023.01.01,00:10,1.0515,1.0530,1.0510,1.0520,800
```

### TradingView Format
```csv
time,open,high,low,close,volume
2023-01-01T00:00:00.000Z,1.0500,1.0520,1.0495,1.0510,1000
2023-01-01T00:05:00.000Z,1.0510,1.0525,1.0505,1.0515,1200
2023-01-01T00:10:00.000Z,1.0515,1.0530,1.0510,1.0520,800
```

## Validation and Troubleshooting

### List Available Files
```bash
python main.py --list-local-files
```

### Validate Specific File
```bash
python main.py --validate-local AUDUSD 5m
```

### Common Issues and Solutions

#### File Not Found
```
Error: No local data file found for AUDUSD 5m
```
**Solutions:**
- Check file naming matches the pattern
- Verify file is in the correct directory
- Use `--list-local-files` to see available files

#### Invalid CSV Format
```
Error: Cannot read CSV file
```
**Solutions:**
- Check CSV delimiter (comma, semicolon, tab)
- Ensure file encoding is UTF-8
- Verify no special characters in headers

#### Missing OHLC Columns
```
Error: Missing required columns: ['Open', 'High']
```
**Solutions:**
- Check column headers match OHLC format
- Ensure case-insensitive matching (open = Open)
- Add missing columns if necessary

#### Invalid Datetime Format
```
Error: Error converting datetime column
```
**Solutions:**
- Check datetime format consistency
- Ensure no missing datetime values
- Specify correct datetime column name

#### Invalid OHLC Relationships
```
Warning: Found 5 rows with invalid OHLC relationships
```
**Solutions:**
- Check High >= Low, Open, Close
- Check Low <= High, Open, Close
- Remove or fix invalid data rows

## Performance Tips

### File Organization
- Keep files in separate directories by source
- Use consistent naming conventions
- Remove unnecessary columns to reduce file size

### Data Quality
- Ensure no missing data gaps
- Validate OHLC relationships
- Use consistent datetime formats
- Remove duplicate timestamps

### Caching
- Enable fallback to online sources for missing data
- System automatically caches downloaded data locally
- Use cached data for faster subsequent runs

## Advanced Configuration

### Multiple Data Sources
```yaml
# Use local data with online fallback
data:
  source: "local"
  fallback_to_online: true
  local_data_path: "data/primary/"
  
# Alternative configuration for different symbols
symbols:
  AUDUSD:
    source: "local"
    local_data_path: "data/mt5/"
  EURUSD:
    source: "yfinance"
```

### Custom File Patterns
```yaml
data:
  source: "local"
  local_file_pattern: "{symbol}_{timeframe}_{source}.csv"
  # Matches: AUDUSD_5m_MT5.csv, EURUSD_1h_TV.csv
```

### Timezone Handling
```yaml
data:
  source: "local"
  timezone: "America/New_York"  # Convert to specific timezone
  datetime_column: "Timestamp"   # Custom datetime column name
```

## Best Practices

1. **Consistent Naming**: Use the same naming pattern for all files
2. **Quality Control**: Validate files before using them
3. **Backup Data**: Keep original files as backups
4. **Version Control**: Track data file versions and sources
5. **Documentation**: Document data sources and any preprocessing
6. **Testing**: Test with small date ranges first
7. **Fallback**: Enable online fallback for missing data

## Troubleshooting Checklist

- [ ] File exists in the specified directory
- [ ] File name matches the configured pattern
- [ ] CSV file has proper OHLC columns
- [ ] Datetime column is properly formatted
- [ ] No missing or invalid data
- [ ] File encoding is UTF-8
- [ ] Directory permissions allow reading
- [ ] Date range overlaps with available data

For additional help, use the validation commands or check the log files for detailed error messages.
