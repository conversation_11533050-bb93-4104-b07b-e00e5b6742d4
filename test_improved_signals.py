#!/usr/bin/env python3
"""
Test improved AUDNZD signal generation after fixes.
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from forex_backtest import load_config, DataLoader, BacktestEngine, EMACrossStrategy


def test_signal_generation():
    """Test signal generation with improved parameters."""
    print("🧪 TESTING IMPROVED SIGNAL GENERATION")
    print("=" * 50)
    
    # Load configuration
    config = load_config('config/settings.yaml')
    
    print(f"Strategy parameters:")
    print(f"  EMA Fast: {config['strategy']['ema_fast']}")
    print(f"  EMA Slow: {config['strategy']['ema_slow']}")
    print(f"  Min EMA Separation: {config['strategy']['min_ema_separation']}")
    print(f"  Confirmation Bars: {config['strategy']['confirmation_bars']}")
    
    # Load data with longer period
    try:
        data_loader = DataLoader({
            'data': {
                'source': 'yfinance',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31'
            }
        })
        
        market_data = data_loader.load_data('AUDNZD', '2024-01-01', '2024-12-31', '1d')
        print(f"\n✅ Data loaded: {len(market_data.data)} bars")
        print(f"Date range: {market_data.data.index[0]} to {market_data.data.index[-1]}")
        
        # Initialize strategy
        strategy = EMACrossStrategy(config)
        
        # Generate signals
        signals_df = strategy.generate_signals(market_data.data)
        
        # Analyze signal counts
        raw_signals = (signals_df['Raw_Signal'] != 0).sum()
        filtered_signals = (signals_df['Filtered_Signal'] != 0).sum()
        confirmed_signals = (signals_df['Confirmed_Signal'] != 0).sum()
        trading_signals = (signals_df['Trading_Signal'] != 0).sum()
        strategy_signals = (signals_df['Strategy_Signal'] != 0).sum()
        
        print(f"\n📊 Signal Analysis:")
        print(f"  Raw crossover signals: {raw_signals}")
        print(f"  After anti-whipsaw filter: {filtered_signals}")
        print(f"  After confirmation filter: {confirmed_signals}")
        print(f"  Final trading signals: {trading_signals}")
        print(f"  Strategy signals: {strategy_signals}")
        
        # Show actual signals
        if strategy_signals > 0:
            print(f"\n🎯 Generated Signals:")
            signal_dates = signals_df.index[signals_df['Strategy_Signal'] != 0]
            signal_values = signals_df['Strategy_Signal'][signals_df['Strategy_Signal'] != 0]
            
            for date, signal in zip(signal_dates, signal_values):
                signal_type = "BUY" if signal > 0 else "SELL"
                print(f"  {date.strftime('%Y-%m-%d')}: {signal_type}")
        
        return strategy_signals > 0, market_data, strategy
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None, None


def test_full_backtest():
    """Test full backtest with improved parameters."""
    print("\n🧪 TESTING FULL BACKTEST")
    print("=" * 50)
    
    success, market_data, strategy = test_signal_generation()
    
    if not success:
        print("❌ Signal generation failed, skipping backtest")
        return
    
    try:
        # Load configuration
        config = load_config('config/settings.yaml')
        
        # Override dates to use 2024 data (available)
        config['data']['start_date'] = '2024-01-01'
        config['data']['end_date'] = '2024-12-31'
        config['trading']['timeframe'] = '1d'
        
        # Initialize backtest engine
        backtest_engine = BacktestEngine(strategy, market_data, config)
        
        # Run backtest
        print("Running backtest...")
        results = backtest_engine.run()
        
        # Display results
        performance = results.performance_summary
        
        print(f"\n📈 Backtest Results:")
        print(f"  Initial Balance: ${performance['initial_balance']:,.2f}")
        print(f"  Final Equity: ${performance['final_equity']:,.2f}")
        print(f"  Total Return: {performance['total_return_percent']:+.2f}%")
        print(f"  Total Trades: {performance['total_trades']}")
        
        if performance['total_trades'] > 0:
            print(f"  Win Rate: {performance['win_rate_percent']:.1f}%")
            print(f"  Profit Factor: {performance.get('profit_factor', 'N/A')}")
            print(f"  Max Drawdown: {performance.get('max_drawdown_percent', 'N/A'):.2f}%")
            
            # Show trade details
            if hasattr(results, 'trades') and len(results.trades) > 0:
                print(f"\n📋 Trade Summary:")
                for i, trade in enumerate(results.trades[:5]):  # Show first 5 trades
                    print(f"  Trade {i+1}: {trade.side.value} at {trade.entry_time.strftime('%Y-%m-%d')} "
                          f"-> {trade.exit_time.strftime('%Y-%m-%d') if trade.exit_time else 'Open'}")
        else:
            print("  ❌ No trades executed")
        
        return performance['total_trades'] > 0
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_timeframes():
    """Test signal generation with different timeframes."""
    print("\n🧪 TESTING DIFFERENT TIMEFRAMES")
    print("=" * 50)
    
    timeframes = ['1d']  # Start with daily, can add others if yfinance supports them
    
    for timeframe in timeframes:
        print(f"\n📊 Testing {timeframe} timeframe:")
        
        try:
            config = load_config('config/settings.yaml')
            
            data_loader = DataLoader({
                'data': {
                    'source': 'yfinance',
                    'start_date': '2024-01-01',
                    'end_date': '2024-12-31'
                }
            })
            
            market_data = data_loader.load_data('AUDNZD', '2024-01-01', '2024-12-31', timeframe)
            
            strategy = EMACrossStrategy(config)
            signals_df = strategy.generate_signals(market_data.data)
            
            strategy_signals = (signals_df['Strategy_Signal'] != 0).sum()
            print(f"  Data bars: {len(market_data.data)}")
            print(f"  Strategy signals: {strategy_signals}")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")


def main():
    """Run comprehensive testing of improved signal generation."""
    print("🚀 AUDNZD IMPROVED SIGNAL GENERATION TEST")
    print("=" * 60)
    
    # Test 1: Signal generation
    signal_success, _, _ = test_signal_generation()
    
    # Test 2: Full backtest if signals work
    if signal_success:
        backtest_success = test_full_backtest()
        
        if backtest_success:
            print(f"\n✅ SUCCESS: AUDNZD trading signals are now working!")
            print("The system is generating trades and executing backtests properly.")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: Signals generated but no trades executed.")
            print("Check position sizing, risk management, or order execution logic.")
    else:
        print(f"\n❌ FAILURE: Still no trading signals generated.")
        print("Further parameter adjustments may be needed.")
    
    # Test 3: Different timeframes
    test_different_timeframes()
    
    print(f"\n🎯 NEXT STEPS:")
    print("1. If successful, run: python main.py --start 2024-01-01 --end 2024-12-31")
    print("2. Use daily timeframe for more reliable signals: --timeframe 1d")
    print("3. Monitor logs for detailed signal generation information")
    print("4. Adjust EMA periods further if needed (try 12/26 for more signals)")


if __name__ == '__main__':
    main()
